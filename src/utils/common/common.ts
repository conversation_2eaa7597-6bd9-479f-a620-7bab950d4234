import type { GetProp, UploadProps } from 'antd';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { toast } from 'react-toastify';

import { handleApiError } from '@/utils/api/apiHelpers';
import baseApi from '@/utils/api/baseApi';

type ToastType = 'info' | 'success' | 'warning' | 'error' | 'default';
export type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

/**
 * Show a toast notification with specified type
 * @param message Message to display
 * @param type Type of notification
 */
export const notify = (message: string, type: ToastType = 'default'): void => {
  switch (type) {
    case 'info':
      toast.info(message);
      break;
    case 'success':
      toast.success(message);
      break;
    case 'warning':
      toast.warning(message);
      break;
    case 'error':
      toast.error(message);
      break;
    default:
      toast(message);
      break;
  }
};

/**
 * Format date to specified format
 * @param date Date to format
 * @param format Output format
 * @returns Formatted date string
 */
export const formatDate = (date: string | Date | Dayjs | null | undefined, format: string = 'DD/MM/YYYY'): string => {
  if (!date) {return '';}
  return dayjs(date).format(format);
};

/**
 * Format datetime to DD/MM/YYYY HH:mm:ss format
 * @param date Date to format
 * @returns Formatted datetime string
 */
export const formatDateTime = (date: string | Date | Dayjs | null | undefined): string => {
  if (!date) {return '';}
  return dayjs(date).format('DD/MM/YYYY HH:mm:ss');
};

/**
 * Handle API and network errors
 * @param error Error object
 */
export const handleError = (error: AxiosError | AxiosResponse | any): void => {
  // Use our new API error handler
  handleApiError(error);

  // Maintain backward compatibility with existing code
  if (error?.response?.data?.message) {
    notify(error.response.data.message, 'error');
  } else if (error?.message) {
    notify(error.message, 'error');
  } else {
    notify('Đã xảy ra lỗi không mong muốn', 'error');
  }
};

export const readVietnameseCurrency = (amount: number): String => {
  if (amount >= 1000000000) {
    if (amount % 1000000000 === 0) {
      return `${amount / 1000000000} tỷ`;
    }
    return `${(amount / 1000000000).toFixed(2)} tỷ`;
  }
  if (amount >= 1000000) {
    if (amount % 1000000 === 0) {
      return `${amount / 1000000} triệu`;
    }
    return `${(amount / 1000000).toFixed(2)} triệu`;
  }
  return `${amount.toLocaleString('vi-VN')} đồng`;
};

// so sánh thời gian hiện tại với thời gian truyền vào , trả về kiều 1 ngày trước, 1 giờ trước ....
export const checkTimeAgo = (time: string) => {
  const now = dayjs();
  const differenceInHours = now.diff(time, 'hour');
  const differenceInDays = now.diff(time, 'day');

  if (differenceInDays > 0) {
    return `${differenceInDays} ngày trước`;
  }
  if (differenceInHours >= 1) {
    return `${differenceInHours} giờ trước`;
  }
  return 'Vài phút trước';
};

export const getRequest = (url: string, config?: AxiosRequestConfig): Promise<any> => {
  // Build URL with query params for logging (filter out undefined values)
  let queryString = '';
  if (config?.params) {
    const filteredParams = Object.entries(config.params)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(filteredParams).length > 0) {
      queryString = '?' + new URLSearchParams(filteredParams).toString();
    }
  }
  const fullUrl = url + queryString;
  console.log('Common GET Request:', fullUrl);

  return new Promise((resolve, reject) => {
    baseApi
      .get(url, config)
      .then((res: AxiosResponse) => {
        console.log('Common GET Response:', { url: fullUrl, data: res?.data });
        resolve(res?.data);
      })
      .catch((err: AxiosError) => {
        console.error('Common GET Error:', { url: fullUrl, error: err });
        reject(err);
      });
  });
};

export const getFileRequest = (url: string, config?: AxiosRequestConfig, fileName?: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    baseApi
      .get(url, config)
      .then((res: AxiosResponse) => {
        const contentDispositionHeader = res.headers['content-disposition'];

        // if (contentDispositionHeader) {
        //   // Xử lý thông tin trong header Content-Disposition ở đây
        //   console.log(decodeURIComponent(contentDispositionHeader));
        // } else {
        //   console.log('Không tìm thấy dữ liệu.');
        // }
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;

        const matches = filenameRegex.exec(contentDispositionHeader);

        const filename = matches && matches[1] ? matches[1].replace(/['"]/g, '') : fileName || 'unknown';

        // console.log('Tên tệp tin:', decodeURIComponent(filename));
        const blob = new Blob([res.data], {
          type: res.headers['content-type'],
        });
        saveAs(blob, decodeURIComponent(filename) || 'File lỗi');
        return resolve(res?.data);
      })
      .catch((err: AxiosError) => reject(err));
  });
};

export const postFileRequest = (
  url: string,
  data: any,
  fileName?: string,
  config?: AxiosRequestConfig,
): Promise<any> => {
  return new Promise((resolve, reject) => {
    baseApi
      .post(url, data, config)
      .then((res: AxiosResponse) => {
        const contentDispositionHeader = res.headers['content-disposition'];

        // if (contentDispositionHeader) {
        //   // Xử lý thông tin trong header Content-Disposition ở đây
        //   console.log(decodeURIComponent(contentDispositionHeader));
        // } else {
        //   console.log('Không tìm thấy dữ liệu.');
        // }
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;

        const matches = filenameRegex.exec(contentDispositionHeader);

        const filename = matches && matches[1] ? matches[1].replace(/['"]/g, '') : fileName || 'unknown';

        // console.log('Tên tệp tin:', decodeURIComponent(filename));
        const blob = new Blob([res.data], {
          type: res.headers['content-type'],
        });
        saveAs(blob, decodeURIComponent(filename) || 'File lỗi');
        return resolve(res?.data);
      })
      .catch((err: AxiosError) => reject(err));
  });
};

export const postRequest = (url: string, data: any, config?: AxiosRequestConfig): Promise<any> => {
  // Build URL with query params for logging (filter out undefined values)
  let queryString = '';
  if (config?.params) {
    const filteredParams = Object.entries(config.params)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(filteredParams).length > 0) {
      queryString = '?' + new URLSearchParams(filteredParams).toString();
    }
  }
  const fullUrl = url + queryString;
  console.log('Common POST Request:', fullUrl, 'Body:', data);

  return new Promise((resolve, reject) => {
    baseApi
      .post(url, data, config)
      .then((res: AxiosResponse) => {
        console.log('Common POST Response:', { url: fullUrl, data: res?.data });
        resolve(res?.data);
      })
      .catch((err: AxiosError) => {
        console.error('Common POST Error:', { url: fullUrl, body: data, error: err });
        reject(err);
      });
  });
};

export const putRequest = (url: string, data: any, config?: AxiosRequestConfig): Promise<any> => {
  // Build URL with query params for logging (filter out undefined values)
  let queryString = '';
  if (config?.params) {
    const filteredParams = Object.entries(config.params)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(filteredParams).length > 0) {
      queryString = '?' + new URLSearchParams(filteredParams).toString();
    }
  }
  const fullUrl = url + queryString;
  console.log('Common PUT Request:', fullUrl, 'Body:', data);

  return new Promise((resolve, reject) => {
    baseApi
      .put(url, data, config)
      .then((res: AxiosResponse) => {
        console.log('Common PUT Response:', { url: fullUrl, data: res?.data });
        resolve(res?.data);
      })
      .catch((err: AxiosError) => {
        console.error('Common PUT Error:', { url: fullUrl, body: data, error: err });
        reject(err);
      });
  });
};

export const downloadFile = (data: any, fileName: string): void => {
  const blob = new Blob(data);
  saveAs(blob, fileName);
};

//  hàm convert string sang number
export const convertStringToNumber = (input: string): number | null => {
  const numberValue: number = parseFloat(input);
  if (!numberValue) {
    return null;
  }
  return numberValue;
};

// hàm convert từ string sang boolean
export const stringToBoolean = (value: string): boolean => {
  const lowerCaseValue = value && value.toLowerCase().trim();
  if (lowerCaseValue === 'true' || lowerCaseValue === '1') {
    return true;
  }
  if (lowerCaseValue === 'false' || lowerCaseValue === '0') {
    return false;
  }
  return false;
};

export const deleteRequest = (url: string, config?: AxiosRequestConfig): Promise<any> => {
  return new Promise((resolve, reject) => {
    baseApi
      .delete(url, config)
      .then((res: AxiosResponse) => resolve(res?.data))
      .catch((err: AxiosError) => reject(err));
  });
};

export const onCheckErrorCode = (val?: string, messageId?: string) => {
  if (val && messageId) {
    const codes: any = sessionStorage?.getItem('code');
    const value = JSON.parse(codes);
    const errorCodeMatchedItem = value?.find((itm: any) => itm?.errorCode === val);
    const messageIdMatchedItem = value?.find((itm: any) => itm?.messageId === messageId);

    if (errorCodeMatchedItem && messageIdMatchedItem) {
      const message = value.find((item: any) => item.messageId === messageId).messageVI;
      return notify(`${message}`, 'error');
    }

    if (messageId === 'Access denied') {
      return notify(`Người dùng không có quyền`, 'error');
    }

    return notify(`${messageId}`, 'error');
  }
  return notify('Lỗi hệ thống, xin vui lòng thử lại', 'error');
};

// Disabled ngày hiện tại
export const disabledDateToDay = (current: Dayjs | null) => {
  // Lấy ngày hôm nay
  const today = dayjs().startOf('day');
  // Chuyển current thành đối tượng Day.js
  const currentDay = dayjs(current).startOf('day');
  // So sánh nếu currentDay trước today
  return currentDay.isAfter(today);
};

export const getBase64 = (file: FileType): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

export const getCookie = (name: any) => {
  const value = `; ${document.cookie}`;
  const parts: any = value.split(`; ${name}=`);
  if (parts.length === 2) {return parts.pop().split(';').shift();}
};
