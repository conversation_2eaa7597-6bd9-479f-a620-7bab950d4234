/**
 * Application-wide constants
 */
// Auth-related constants
export const ACCESS_TOKEN = 'access_token';
export const REFRESH_TOKEN = 'refresh_token';
export const AUTHCODE = 'authCode';

// Environment configuration
export const PUBLIC_PATH = '/monitor'; // Base path for the application

// Application constants
export const APP_CODE = '028';
export const APP_NAME = 'Monitor';

// Pagination defaults
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_CURRENT_PAGE = 1;
export const PAGE_SIZE_OPTIONS = ['10', '20', '50', '100'];

// Date formats
export const DATE_FORMAT = 'DD/MM/YYYY';
export const DATE_TIME_FORMAT = 'DD/MM/YYYY HH:mm:ss';
export const TIME_FORMAT = 'HH:mm:ss';

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'app_theme',
  LANGUAGE: 'app_language',
  USER_SETTINGS: 'user_settings',
};

// Theme options
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
};

// Language options
export const LANGUAGES = {
  EN: 'en',
  VI: 'vi',
};

// Helper function to safely get environment variables
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  try {
    return (typeof process !== 'undefined' && process.env && process.env[key]) || defaultValue;
  } catch (error) {
    console.warn(`Failed to access environment variable ${key}, using default:`, defaultValue);
    return defaultValue;
  }
};

// Environment detection logic
// ENV values: 'local', 'development', 'staging', 'production'
// If ENV is not set, default to 'local'
export const CURRENT_ENV = getEnvVar('ENV', 'local');
export const NODE_ENV = getEnvVar('NODE_ENV', 'development');

// Environment-based configuration using values directly from .env files
export const BASE_URL = getEnvVar('REACT_APP_API_URL', 'http://localhost:3000');
export const BACKEND_URL_SSO = getEnvVar('BACKEND_URL_SSO', 'http://localhost:3000/v1/qtud-sso/');
export const BACKEND_URL_APP = getEnvVar('BACKEND_URL_APP', 'http://localhost:3000/v1/qtud-app/');
export const LOGOUT_REDIRECT_URL = getEnvVar('REACT_APP_LOGOUT_REDIRECT_URL', 'http://localhost:3000');
export const BACKEND_URL_EXTERNAL_SERVICE = getEnvVar('REACT_APP_BACKEND_URL_EXTERNAL_SERVICE', 'http://localhost:3000');

// API Access Token - hardcoded for external API environments
export const API_ACCESS_TOKEN = 'eyJraWQiOiI3NzUxOWM5MC0wYjg4LTQzMzQtOTZjMi1hNzU2MWE1ODUyYWEiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************.AHBxGYO3z3yMmcTdWKXPvR9ajuB4iExnJwj-Kw6C7hnxDXZdqAPKZ4LqVEf3VPkMUdixXPzxld50y94cnOcZPKz6_2XRfJ_Sp1JMN0xc0nVJA_b67cDU4jiWITCRL-o1_nthWWZYj9XVjMmpC8u4mGnK_6PtfvdbVLKkexxupmJQAClFS5l1xhaphJ8iEPoeeOlQZ7tfphLZ-THNBMafePTtx7RZ_Igpce9x4foAVZ5nSFYpezbezVvumNqlHGNV2zBlqvGcJ-oxuAWQjeiUBIPnujfx8xWXspHRzReouaVqW2I0YKlX2uqTb-SNyGVOQytPeOIuo2Ej98aNZ5P8mw';

// API Endpoints - constructed from environment variables
export const BACKEND_URL_KAFKA_MONITOR = `${BACKEND_URL_EXTERNAL_SERVICE}/monitor/kafka`;
export const BACKEND_URL_MESSAGE_MONITOR = `${BACKEND_URL_EXTERNAL_SERVICE}/monitor/message`;

// Environment info for debugging
console.log(`🌍 Environment: ${CURRENT_ENV} (NODE_ENV: ${NODE_ENV})`);
console.log(`🔗 API Base: ${BACKEND_URL_EXTERNAL_SERVICE}`);
console.log(`🔗 SSO URL: ${BACKEND_URL_SSO}`);
console.log(`🔗 APP URL: ${BACKEND_URL_APP}`);