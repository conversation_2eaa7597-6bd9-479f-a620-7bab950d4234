import React, { useRef, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, Flex } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { THEME_COLORS } from '@/utils/ui';
import { z } from 'zod';
import { FormRef } from '@/components/form/Form';
import { Form, FloatingSelectField, FloatingTextField } from '@/components/form';
import dayjs from 'dayjs';
import { useProducerOptions } from '@/hooks/useProducerOptions';
import { messageSentStatusOptions } from './constants';
import { MessageMonitorApi } from '@/api/message-monitor';



// Dashboard filter form schema - no validation
const dashboardFilterSchema = z.object({
    appCode: z.string().nullable(),
    topic: z.string().nullable(),
    sentStatus: z.string().nullable(),
    traceId: z.string().nullable(),
    sentDateRange: z.array(z.date()).nullable(),
    createdTime: z.date().nullable(),
    lastProcessedTime: z.date().nullable(),
});

export type DashboardFilterFormData = z.infer<typeof dashboardFilterSchema>;

// Time range options similar to Kibana
const timeRangeOptions = [
    { key: 'now-15m', label: '15 phút qua', value: () => [dayjs().subtract(15, 'minute'), dayjs()] },
    { key: 'now-30m', label: '30 phút qua', value: () => [dayjs().subtract(30, 'minute'), dayjs()] },
    { key: 'now-1h', label: '1 giờ qua', value: () => [dayjs().subtract(1, 'hour'), dayjs()] },
    { key: 'now-3h', label: '3 giờ qua', value: () => [dayjs().subtract(3, 'hour'), dayjs()] },
    { key: 'now-6h', label: '6 giờ qua', value: () => [dayjs().subtract(6, 'hour'), dayjs()] },
    { key: 'now-12h', label: '12 giờ qua', value: () => [dayjs().subtract(12, 'hour'), dayjs()] },
    { key: 'now-24h', label: '24 giờ qua', value: () => [dayjs().subtract(24, 'hour'), dayjs()] },
    { key: 'now-7d', label: '7 ngày qua', value: () => [dayjs().subtract(7, 'day'), dayjs()] },
    { key: 'now-30d', label: '30 ngày qua', value: () => [dayjs().subtract(30, 'day'), dayjs()] },
    { key: 'today', label: 'Hôm nay', value: () => [dayjs().startOf('day'), dayjs().endOf('day')] },
    { key: 'yesterday', label: 'Hôm qua', value: () => [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
    { key: 'this-week', label: 'Tuần này', value: () => [dayjs().startOf('week'), dayjs().endOf('week')] },
    { key: 'last-week', label: 'Tuần trước', value: () => [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')] },
    { key: 'this-month', label: 'Tháng này', value: () => [dayjs().startOf('month'), dayjs().endOf('month')] },
    { key: 'last-month', label: 'Tháng trước', value: () => [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] },
];

interface MessageMonitorFilterFormProps {
    onDataLoaded: (data: any[], total: number) => void;
    onStatisticsLoaded?: (filters: any) => void;
}

export const MessageMonitorFilterForm: React.FC<MessageMonitorFilterFormProps> = ({
    onDataLoaded,
    onStatisticsLoaded
}) => {
    const formRef = useRef<FormRef>(null);
    const { options: producerOptions, loading: producerLoading } = useProducerOptions();



    const transformFilterToApiParams = (filterData: DashboardFilterFormData) => {
        const params: any = {
            page: 1,
            size: 10,
        };

        if (filterData.appCode) {
            params.appCode = filterData.appCode;
        }
        if (filterData.topic) {
            params.topic = filterData.topic;
        }
        if (filterData.sentStatus) {
            params.sentStatus = Number(filterData.sentStatus);
        }
        if (filterData.traceId) {
            params.traceId = filterData.traceId;
        }
        // Handle date range for sending date with custom format
        if (filterData.sentDateRange && filterData.sentDateRange.length === 2) {
            // Format as yyyy-MM-dd'T'HH:mm:ss
            params.fromDate = dayjs(filterData.sentDateRange[0]).format('YYYY-MM-DDTHH:mm:ss');
            params.toDate = dayjs(filterData.sentDateRange[1]).format('YYYY-MM-DDTHH:mm:ss');
        }
        // Fallback to single date fields if range not provided
        else if (filterData.createdTime || filterData.lastProcessedTime) {
            if (filterData.createdTime) {
                params.fromDate = dayjs(filterData.createdTime).format('YYYY-MM-DDTHH:mm:ss');
            }
            if (filterData.lastProcessedTime) {
                params.toDate = dayjs(filterData.lastProcessedTime).format('YYYY-MM-DDTHH:mm:ss');
            }
        }

        return params;
    };

    const transformToStatisticsParams = (filterData: DashboardFilterFormData) => {
        const params: any = {};

        if (filterData.appCode) {
            params.appCode = filterData.appCode;
        }
        if (filterData.topic) {
            params.topic = filterData.topic;
        }
        if (filterData.sentStatus) {
            params.sentStatus = filterData.sentStatus;
        }
        if (filterData.traceId) {
            params.traceId = filterData.traceId;
        }
        // Handle date range for statistics with custom format
        if (filterData.sentDateRange && filterData.sentDateRange.length === 2) {
            // Format as yyyy-MM-dd'T'HH:mm:ss
            params.fromDate = dayjs(filterData.sentDateRange[0]).format('YYYY-MM-DDTHH:mm:ss');
            params.toDate = dayjs(filterData.sentDateRange[1]).format('YYYY-MM-DDTHH:mm:ss');
            
            // Calculate optimal interval based on date range
            const fromDate = dayjs(filterData.sentDateRange[0]);
            const toDate = dayjs(filterData.sentDateRange[1]);
            const diffInMinutes = toDate.diff(fromDate, 'minute');
            const diffInHours = toDate.diff(fromDate, 'hour');
            const diffInDays = toDate.diff(fromDate, 'day');
            
            // Define interval rules based on time range duration
            let interval: string;
            if (diffInMinutes <= 60) {
                interval = '5m';        // 1 hour or less: 5-minute intervals
            } else if (diffInHours <= 6) {
                interval = '15m';       // 6 hours or less: 15-minute intervals
            } else if (diffInHours <= 24) {
                interval = '1h';        // 1 day or less: 1-hour intervals
            } else if (diffInDays <= 7) {
                interval = '6h';        // 1 week or less: 6-hour intervals
            } else if (diffInDays <= 30) {
                interval = '1d';        // 1 month or less: 1-day intervals
            } else if (diffInDays <= 90) {
                interval = '3d';        // 3 months or less: 3-day intervals
            } else {
                interval = '1w';        // More than 3 months: 1-week intervals
            }
            
            params.interval = interval;
        }
        // Fallback to single date fields
        else if (filterData.createdTime || filterData.lastProcessedTime) {
            if (filterData.createdTime) {
                params.fromDate = dayjs(filterData.createdTime).format('YYYY-MM-DDTHH:mm:ss');
            }
            if (filterData.lastProcessedTime) {
                params.toDate = dayjs(filterData.lastProcessedTime).format('YYYY-MM-DDTHH:mm:ss');
            }
            // Default interval for single date fields
            params.interval = '1h';
        } else {
            // Default interval when no date range is provided
            params.interval = '6h';
        }

        return params;
    };

    const handleSearch = useCallback(async (filterData: DashboardFilterFormData) => {
        try {
            console.log('Dashboard search with filter data:', filterData);
            
            // Call message search API
            const apiParams = transformFilterToApiParams(filterData);
            console.log('Dashboard API params:', apiParams);

            const response = await MessageMonitorApi.getAll(apiParams);
            console.log('Dashboard API response:', response);

            if (response.success && response.data) {
                onDataLoaded(response.data.content || [], response.data.totalElements || 0);
            }

            // Call statistics API if callback is provided
            if (onStatisticsLoaded) {
                const statisticsParams = transformToStatisticsParams(filterData);
                console.log('Dashboard statistics params:', statisticsParams);
                onStatisticsLoaded(statisticsParams);
            }
        } catch (error) {
            console.error('Dashboard search error:', error);
        }
    }, []); // Remove dependencies to prevent recreation

    // Load initial data on mount with default time range (7 days)
    useEffect(() => {
        const initialSearch = async () => {
            // Set default time range (7 days ago)
            const defaultOption = timeRangeOptions.find(opt => opt.key === 'now-7d');
            const [fromDate, toDate] = defaultOption ? defaultOption.value() : [dayjs().subtract(7, 'day'), dayjs()];
            
            const initialData = {
                appCode: null,
                topic: null,
                sentStatus: null,
                traceId: null,
                sentDateRange: [fromDate.toDate(), toDate.toDate()],
                createdTime: null,
                lastProcessedTime: null,
            };
            
            // Set form values
            formRef.current?.setValue('sentDateRange', [fromDate.toDate(), toDate.toDate()]);
            
            try {
                console.log('Dashboard initial search with filter data:', initialData);
                
                // Call message search API
                const apiParams = transformFilterToApiParams(initialData);
                console.log('Dashboard initial API params:', apiParams);

                const response = await MessageMonitorApi.getAll(apiParams);
                console.log('Dashboard initial API response:', response);

                if (response.success && response.data) {
                    onDataLoaded(response.data.content || [], response.data.totalElements || 0);
                }

                // Call statistics API if callback is provided
                if (onStatisticsLoaded) {
                    const statisticsParams = transformToStatisticsParams(initialData);
                    console.log('Dashboard initial statistics params:', statisticsParams);
                    onStatisticsLoaded(statisticsParams);
                }
            } catch (error) {
                console.error('Dashboard initial search error:', error);
            }
        };
        
        initialSearch();
    }, []); // Empty dependency array - run only once on mount

    const handleSubmit = (data: DashboardFilterFormData) => {
        console.log('Dashboard form submitted:', data);
        handleSearch(data);
    };

    const handleReset = () => {
        console.log('Dashboard form reset');
        formRef.current?.reset();
        handleSearch({
            appCode: null,
            topic: null,
            sentStatus: null,
            traceId: null,
            sentDateRange: null,
            createdTime: null,
            lastProcessedTime: null,
        });
    };

    const handleManualSearch = () => {
        const formValues = formRef.current?.getValues();
        console.log('Dashboard manual search with values:', formValues);
        if (formValues) {
            handleSearch(formValues);
        }
    };



    return (
        <div>

            <Form
                ref={formRef}
                onSubmit={handleSubmit}
                schema={dashboardFilterSchema}
                defaultValues={{
                    appCode: null,
                    topic: null,
                    sentStatus: null,
                    traceId: null,
                    sentDateRange: null,
                    createdTime: null,
                    lastProcessedTime: null,
                }}
            >
                {/* Filter fields and action buttons in same row */}
                <Flex gap="middle" className="mb-4" wrap="wrap" align="center">
                    <div style={{ flex: 1, minWidth: '150px' }}>
                        <FloatingSelectField
                            name="appCode"
                            floatingLabel="Hệ thống gửi"
                            options={producerOptions}
                            allowClear
                            loading={producerLoading}
                        />
                    </div>
                    <div style={{ flex: 1, minWidth: '150px' }}>
                        <FloatingTextField
                            name="topic"
                            floatingLabel="Topic"
                        />
                    </div>
                    <div style={{ flex: 1, minWidth: '150px' }}>
                        <FloatingSelectField
                            name="sentStatus"
                            floatingLabel="Trạng thái gửi"
                            options={messageSentStatusOptions}
                            allowClear
                        />
                    </div>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                        <FloatingTextField
                            name="traceId"
                            floatingLabel="Mã nghiệp vụ"
                        />
                    </div>

                    {/* Action buttons with text */}
                    <div style={{ display: 'flex', gap: '8px', flexShrink: 0, alignItems: 'center' }}>
                        <Button
                            type="primary"
                            icon={<SearchOutlined />}
                            onClick={handleManualSearch}
                            style={{
                                backgroundColor: THEME_COLORS.BASE_COLOR,
                                borderColor: THEME_COLORS.BASE_COLOR
                            }}
                        >
                            Tìm kiếm
                        </Button>
                        <Button
                            icon={<ReloadOutlined />}
                            onClick={handleReset}
                        >
                            Làm mới
                        </Button>
                    </div>
                </Flex>
            </Form>
        </div>
    );
};
