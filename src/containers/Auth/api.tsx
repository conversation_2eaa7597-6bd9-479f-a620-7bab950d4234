import { BACKEND_URL_APP_FINAL, BACKEND_URL_SSO_FINAL } from '@/utils';
import baseApi from '@/utils/api/baseApi';
import type { AxiosResponse } from 'axios';


// GET
export const apiGetInfoUser = (url: string, params?: Record<string, any>): Promise<any> => {
  // Build URL with query params for logging (filter out undefined values)
  let queryString = '';
  if (params) {
    const filteredParams = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(filteredParams).length > 0) {
      queryString = '?' + new URLSearchParams(filteredParams).toString();
    }
  }
  const fullUrl = `${BACKEND_URL_APP_FINAL}${url}${queryString}`;
  console.log('Auth apiGetInfoUser Request:', fullUrl);

  return new Promise((resolve, reject) => {
    baseApi
      .get(`${BACKEND_URL_APP_FINAL}${url}`, { params })
      .then((res: AxiosResponse) => {
        console.log('Auth apiGetInfoUser Response:', { url: fullUrl, data: res?.data });
        resolve(res?.data);
      })
      .catch((err: Error) => {
        console.error('Auth apiGetInfoUser Error:', { url: fullUrl, error: err });
        reject(err);
      });
  });
};
// GET
export const apiGetToken = (url: string, params?: Record<string, any>): Promise<any> => {
  // Build URL with query params for logging (filter out undefined values)
  let queryString = '';
  if (params) {
    const filteredParams = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(filteredParams).length > 0) {
      queryString = '?' + new URLSearchParams(filteredParams).toString();
    }
  }
  const fullUrl = `${BACKEND_URL_SSO_FINAL}${url}${queryString}`;
  console.log('Auth apiGetToken Request:', fullUrl);

  return new Promise((resolve, reject) => {
    baseApi
      .get(`${BACKEND_URL_SSO_FINAL}${url}`, { params })
      .then((res: AxiosResponse) => {
        console.log('Auth apiGetToken Response:', { url: fullUrl, data: res?.data });
        resolve(res?.data);
      })
      .catch((err: Error) => {
        console.error('Auth apiGetToken Error:', { url: fullUrl, error: err });
        reject(err);
      });
  });
};
