import React, { <PERSON> } from 'react';
import { <PERSON><PERSON>, Button, Space, Spin, Row, Col, Card } from 'antd';
import { KafkaMonitorItem } from '@/api/kafka-monitor';
import { useQuery } from '@tanstack/react-query';
import { KafkaMonitorApi } from '@/api/kafka-monitor';
import { trangThaiDisplayOptions, loaiOptions, phanMemOptions } from './constants';
import { formatDateTime } from '@/utils/common/common';
import { THEME_COLORS } from '@/utils/ui';

interface KafkaMonitorDetailModalProps {
    visible: boolean;
    recordId?: string;
    maUngDung?: string;
    onClose: () => void;
    onResend: (id: string) => void;
    onMarkProcessed: (id: string) => void;
}

const KafkaMonitorDetailModal: FC<KafkaMonitorDetailModalProps> = ({
    visible,
    recordId,
    maUngDung,
    onClose,
    onResend,
    onMarkProcessed
}) => {
    const {
        data: kafkaMonitorDetail,
        isLoading,
        error
    } = useQuery({
        queryKey: ['kafkaMonitor', 'detail', maUngDung, recordId],
        queryFn: async () => {
            if (!recordId || !maUngDung) return null;
            const response = await KafkaMonitorApi.getById(maUngDung, recordId);
            if (!response.success) {
                throw new Error(response.message || 'Failed to fetch details');
            }
            return response.data;
        },
        enabled: !!recordId && !!maUngDung && visible
    });

    const handleResend = () => {
        if (recordId) {
            onResend(recordId);
        }
    };

    const handleMarkProcessed = () => {
        if (recordId) {
            onMarkProcessed(recordId);
        }
    };

    // Helper function to find label by value in options array
    const getLabelByValue = (value: string, options: { label: string; value: string }[]) => {
        const option = options.find(opt => opt.value === value);
        return option ? option.label : value;
    };



    // Helper function to render JSON in a nice format
    const renderJsonField = (jsonString: string | null, maxHeight = '150px') => {
        if (!jsonString) return <span></span>;

        try {
            const parsed = JSON.parse(jsonString);
            return (
                <pre style={{
                    whiteSpace: 'pre-wrap',
                    fontSize: '12px',
                    maxHeight: maxHeight,
                    overflow: 'auto',
                    backgroundColor: '#f5f5f5',
                    padding: '8px',
                    borderRadius: '4px',
                    border: '1px solid #d9d9d9',
                    margin: 0,
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
                }}>
                    {JSON.stringify(parsed, null, 2)}
                </pre>
            );
        } catch (error) {
            return (
                <span style={{
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    fontSize: '12px',
                    color: '#666'
                }}>
                    {jsonString}
                </span>
            );
        }
    };

    // Helper function to render text or JSON field with proper formatting
    const renderTextOrJsonField = (content: string | null, maxHeight = '150px') => {
        if (!content) return <span></span>;

        // Try to parse as JSON first
        try {
            const parsed = JSON.parse(content);
            return (
                <pre style={{
                    whiteSpace: 'pre-wrap',
                    fontSize: '12px',
                    maxHeight: maxHeight,
                    overflow: 'auto',
                    backgroundColor: '#f5f5f5',
                    padding: '8px',
                    borderRadius: '4px',
                    border: '1px solid #d9d9d9',
                    margin: 0,
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
                }}>
                    {JSON.stringify(parsed, null, 2)}
                </pre>
            );
        } catch (error) {
            // If not JSON, render as plain text with nice formatting
            return (
                <div style={{
                    fontSize: '13px',
                    maxHeight: maxHeight,
                    overflow: 'auto',
                    backgroundColor: '#f5f5f5',
                    padding: '8px',
                    borderRadius: '4px',
                    border: '1px solid #d9d9d9',
                    color: '#333',
                    lineHeight: '1.5'
                }}>
                    {content}
                </div>
            );
        }
    };

    return (
        <Drawer
            title={
                <div style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: '#ffffff',
                    margin: 0,
                    padding: 0,
                    display: 'block'
                }}>
                    Chi tiết bản tin
                </div>
            }
            placement="right"
            open={visible}
            onClose={onClose}
            width={1000}
            styles={{
                header: {
                    backgroundColor: '#1b524f',
                    borderBottom: '1px solid #1b524f',
                    color: '#ffffff',
                    padding: '16px 24px',
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                },
                body: { padding: '0' }
            }}
            closeIcon={
                <span style={{
                    color: '#ffffff',
                    fontSize: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '22px',
                    height: '22px',
                    position: 'relative',
                    zIndex: 10,
                    cursor: 'pointer'
                }}>
                    ✕
                </span>
            }
            className="custom-drawer"
        >
            {isLoading ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Spin size="large" />
                </div>
            ) : error ? (
                <div style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                    <p>Có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại.</p>
                </div>
            ) : kafkaMonitorDetail ? (
                <div style={{ padding: '16px 8px 0 8px' }}>
                    {/* Basic Information Card */}
                    <Card
                        title="Thông tin chung"
                        size="small"
                        style={{ marginBottom: 16 }}
                        styles={{ body: { padding: '12px' } }}
                    >
                        <Row gutter={[16, 8]}>
                            <Col span={8}>
                                <div style={{ fontSize: '13px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Phần mềm: </span>
                                    <span style={{ fontWeight: 500 }}>
                                        {getLabelByValue(kafkaMonitorDetail.maUngDung, phanMemOptions)}
                                    </span>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div style={{ fontSize: '13px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Loại: </span>
                                    <span style={{ fontWeight: 500 }}>
                                        {getLabelByValue(String(kafkaMonitorDetail.guiNhan), loaiOptions)}
                                    </span>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div style={{ fontSize: '13px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Trạng thái: </span>
                                    <span style={{ fontWeight: 500 }}>
                                        {kafkaMonitorDetail.trangThai ? getLabelByValue(String(kafkaMonitorDetail.trangThai), trangThaiDisplayOptions) : ''}
                                    </span>
                                </div>
                            </Col>
                        </Row>
                        <Row gutter={[16, 8]} style={{ marginTop: 12 }}>
                            <Col span={24}>
                                <div style={{ fontSize: '13px', marginBottom: '8px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Send code:</span>
                                </div>
                                <div style={{
                                    fontSize: '11px',
                                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                    backgroundColor: '#f5f5f5',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    border: '1px solid #d9d9d9',
                                    wordBreak: 'break-all'
                                }}>
                                    {kafkaMonitorDetail.sendCode || ''}
                                </div>
                            </Col>
                        </Row>
                    </Card>

                    {/* Date Information */}
                    <Card
                        title="Dữ liệu gửi đi"
                        size="small"
                        style={{ marginBottom: 16 }}
                        styles={{ body: { padding: '12px' } }}
                    >
                        <Row gutter={[16, 8]}>
                            <Col span={8}>
                                <div style={{ fontSize: '13px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Ngày gửi: </span>
                                    <span style={{ fontWeight: 500 }}>
                                        {formatDateTime(kafkaMonitorDetail.ngayGui)}
                                    </span>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div style={{ fontSize: '13px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Ngày nhận phản hồi: </span>
                                    <span style={{ fontWeight: 500 }}>
                                        {formatDateTime(kafkaMonitorDetail.ngayNhanPhanHoi)}
                                    </span>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div style={{ fontSize: '13px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Lần gửi lại: </span>
                                    <span style={{ fontWeight: 500 }}>
                                        {kafkaMonitorDetail.lanGuiLai || ''}
                                    </span>
                                </div>
                            </Col>
                        </Row>
                        <Row style={{ marginTop: 12 }}>
                            <Col span={24}>
                                <div style={{ fontSize: '13px', marginBottom: '8px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Receive Code:</span>
                                </div>
                                <div style={{
                                    fontSize: '11px',
                                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                    backgroundColor: '#f5f5f5',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    border: '1px solid #d9d9d9',
                                    wordBreak: 'break-all'
                                }}>
                                    {kafkaMonitorDetail.receiveCode || ''}
                                </div>
                            </Col>
                        </Row>
                    </Card>

                    {/* Message Headers */}
                    <Card
                        title="Message Headers"
                        size="small"
                        style={{ marginBottom: 16 }}
                        styles={{ body: { padding: '12px' } }}
                    >
                        {renderJsonField(kafkaMonitorDetail.messageHeaders, '150px')}
                    </Card>

                    {/* Nội dung */}
                    <Card
                        title="Nội dung"
                        size="small"
                        style={{ marginBottom: 16 }}
                        styles={{ body: { padding: '12px' } }}
                    >
                        {renderJsonField(kafkaMonitorDetail.noiDung, '150px')}
                    </Card>

                    {/* Ghi chú - only show if there is content */}
                    {kafkaMonitorDetail.ghiChu && (
                        <Card
                            title="Ghi chú"
                            size="small"
                            style={{ marginBottom: 16 }}
                            styles={{ body: { padding: '12px' } }}
                        >
                            {renderTextOrJsonField(kafkaMonitorDetail.ghiChu, '150px')}
                        </Card>
                    )}

                    {/* Action Buttons */}
                    <div style={{
                        position: 'sticky',
                        bottom: 0,
                        backgroundColor: 'white',
                        padding: '16px 0',
                        borderTop: '1px solid #f0f0f0',
                        marginTop: 16
                    }}>
                        <Space style={{ width: '100%', justifyContent: 'center' }}>
                            <Button
                                type="primary"
                                onClick={handleResend}
                                disabled={isLoading || !kafkaMonitorDetail}
                                style={{
                                    backgroundColor: THEME_COLORS.BASE_COLOR,
                                    borderColor: THEME_COLORS.BASE_COLOR
                                }}
                            >
                                Gửi lại
                            </Button>
                            <Button
                                type="primary"
                                onClick={handleMarkProcessed}
                                disabled={isLoading || !kafkaMonitorDetail}
                                style={{
                                    backgroundColor: THEME_COLORS.BASE_COLOR,
                                    borderColor: THEME_COLORS.BASE_COLOR
                                }}
                            >
                                Đã xử lý
                            </Button>
                            <Button
                                onClick={onClose}
                                type="default"
                            >
                                Đóng
                            </Button>
                        </Space>
                    </div>
                </div>
            ) : (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                    <p>Không tìm thấy dữ liệu</p>
                </div>
            )}
        </Drawer>
    );
};

export default KafkaMonitorDetailModal;
