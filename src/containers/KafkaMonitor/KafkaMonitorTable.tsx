import { Button, Dropdown, Spin, Table } from 'antd';
import { EllipsisOutlined } from '@ant-design/icons';
import React, { FC, useMemo } from 'react';
import { KafkaMonitorItem } from '@/api/kafka-monitor';
import { useDashboardStore } from '@/pages/KafkaMonitor/Dashboard/useKafkaMonitorStore';
import { TablePaginationConfig } from 'antd/es/table';
import { useShallow } from 'zustand/react/shallow';
import { formatDateTime } from '@/utils/common/common';
import { trangThaiDisplayOptions, loaiOptions, phanMemOptions } from './constants';

const getColumns = (
  pagination: TablePaginationConfig,
  handleMenuClick: (key: string, record: KafkaMonitorItem) => void
) => [

  {
    title: 'STT',
    key: 'stt',
    width: 40,
    align: 'center' as const,
    render: (_: any, __: any, index: number) => {
      return (pagination.current! - 1) * pagination.pageSize! + index + 1;
    },
  },
  {
    title: 'Loại',
    dataIndex: 'gui<PERSON>han',
    key: 'guiNhan',
    width: 60,
    align: 'center' as const,
    sorter: true,
    render: (value: number) => {
      const option = loaiOptions.find((opt: {label: string; value: string}) => opt.value === String(value));
      return option ? option.label : value;
    },
  },
  {
    title: 'Phần mềm',
    dataIndex: 'maUngDung',
    key: 'maUngDung',
    width: 100,
    align: 'center' as const,
    sorter: true,
    render: (value: string) => {
      const option = phanMemOptions.find((opt: {label: string; value: string}) => opt.value === value);
      return option ? option.label : value;
    },
  },
  {
    title: 'Topic',
    dataIndex: 'topic',
    key: 'topic',
    width: 180,
    align: 'center' as const,
    sorter: true,
  },
  {
    title: 'Mã nghiệp vụ',
    dataIndex: 'maNghiepVu',
    key: 'maNghiepVu',
    width: 100,
    align: 'center' as const,
    sorter: true,
  },
  {
    title: 'Trạng thái',
    dataIndex: 'trangThai',
    key: 'trangThai',
    width: 100,
    align: 'center' as const,
    sorter: true,
    render: (value: number | null) => {
      const option = trangThaiDisplayOptions.find((opt: {label: string; value: string}) => opt.value === String(value));
      return option ? option.label : value === null ? '' : value;
    },
  },

  {
    title: 'Ngày gửi',
    dataIndex: 'ngayGui',
    key: 'ngayGui',
    width: 160,
    align: 'center' as const,
    sorter: true,
    render: (value: string | null) => formatDateTime(value),
  },
  {
    title: 'Ngày nhận phản hồi',
    dataIndex: 'ngayNhanPhanHoi',
    key: 'ngayNhanPhanHoi',
    width: 160,
    align: 'center' as const,
    sorter: true,
    render: (value: string | null) => formatDateTime(value),
  },
  {
    title: 'Thao tác',
    key: 'action',
    width: 60,
    align: 'center' as const,
    render: (_: any, record: KafkaMonitorItem) => (
      <Dropdown
        menu={{
          items: [
            {
              key: 'view',
              label: 'Xem chi tiết',
              onClick: () => handleMenuClick('view', record),
            },
            {
              key: 'resend',
              label: 'Gửi lại',
              onClick: () => handleMenuClick('resend', record),
            },
          ],
        }}
        trigger={['click']}
      >
        <Button type="text" icon={<EllipsisOutlined />} />
      </Dropdown>
    ),
  },
];

const KafkaMonitorTable: FC = () => {
  const {
    kafkaMonitorData,
    pagination,
    selectedRowKeys,
    handleTableChange,
    handleMenuClick,
    isFilterLoading,
    setSelectedRowKeys
  } = useDashboardStore(
    useShallow((state: any) => ({
      kafkaMonitorData: state.kafkaMonitorData,
      pagination: state.pagination,
      selectedRowKeys: state.selectedRowKeys,
      handleTableChange: state.handleTableChange,
      handleMenuClick: state.handleMenuClick,
      isFilterLoading: state.isFilterLoading,
      setSelectedRowKeys: state.setSelectedRowKeys,
    })),
  );

  // Create rowSelection using useMemo to prevent unnecessary recreation
  const rowSelection = useMemo(() => {
    return {
      selectedRowKeys,
      onChange: (newSelectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(newSelectedRowKeys);
      },
    };
  }, [selectedRowKeys, setSelectedRowKeys]);

  // Get columns using the function defined outside the component
  const columns = useMemo(() => getColumns(pagination, handleMenuClick), [pagination, handleMenuClick]);

  return (
    <div style={{
      position: 'relative'
    }}>
      <Spin spinning={isFilterLoading}>
        <Table
          rowKey="id"
          dataSource={kafkaMonitorData}
          columns={columns}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: false,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} bản ghi`,
            position: ['bottomLeft']
          }}
          rowSelection={rowSelection}
          onChange={handleTableChange}
          showSorterTooltip={false}
          scroll={{
            x: 'max-content',
            y: 'calc(100vh - 600px)'
          }}
          style={{
            width: '100%'
          }}
        />
      </Spin>
    </div>
  );
};

export default KafkaMonitorTable;
