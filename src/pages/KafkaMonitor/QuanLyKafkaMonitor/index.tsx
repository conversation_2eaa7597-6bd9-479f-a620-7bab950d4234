import { Card } from "antd";
import { FC, useEffect, useRef } from 'react';
import { useShallow } from 'zustand/react/shallow';

import { useKafkaMonitorStore } from './useKafkaMonitorStore';
import { KafkaMonitorFilterForm } from '@/containers/KafkaMonitor/KafkaMonitorFilterForm';
import KafkaMonitorTable from "@/containers/KafkaMonitor/KafkaMonitorTable";
import KafkaMonitorDetailModal from "@/containers/KafkaMonitor/KafkaMonitorDetailModal";
import StatusPieChart, { StatusPieChartRef } from "@/components/StatusPieChart";
import { THEME_COLORS } from '@/utils/ui';

const QuanLyKafkaMonitor: FC = () => {
  // Chart ref for refreshing chart data
  const chartRef = useRef<StatusPieChartRef>(null);

  // Get state and actions from the store
  const {
    fetchData,
    isDetailModalVisible,
    selectedRecordId,
    filterValues,
    setDetailModalVisible,
    setSelectedRecordId,
    setFilterValues,
    handleResendMessage,
    handleMarkAsProcessed,
    setChartRefreshCallback
  } = useKafkaMonitorStore(
    useShallow((state) => ({
      fetchData: state.fetchData,
      isDetailModalVisible: state.isDetailModalVisible,
      selectedRecordId: state.selectedRecordId,
      filterValues: state.filterValues,
      setDetailModalVisible: state.setDetailModalVisible,
      setSelectedRecordId: state.setSelectedRecordId,
      setFilterValues: state.setFilterValues,
      handleResendMessage: state.handleResendMessage,
      handleMarkAsProcessed: state.handleMarkAsProcessed,
      setChartRefreshCallback: state.setChartRefreshCallback
    }))
  );

  // Handle closing the modal
  const handleCloseModal = () => {
    setDetailModalVisible(false);
    setSelectedRecordId(null);
  };

  // Handle pie chart status click - sync with filter form
  const handleStatusClick = (trangThai: number) => {
    // Update filter values with the selected status
    const newFilterValues = {
      ...filterValues,
      trangThai: String(trangThai)
    };

    // Set the new filter values and trigger search
    setFilterValues(newFilterValues);
    fetchData(newFilterValues).catch(e => console.error(e));
  };

  // Register chart refresh callback
  useEffect(() => {
    const refreshChart = () => {
      if (chartRef.current) {
        chartRef.current.fetchData();
      }
    };
    setChartRefreshCallback(refreshChart);
  }, [setChartRefreshCallback]);

  // Initialize the data fetching on component mount
  useEffect(() => {
    fetchData().catch(e => console.error(e));
  }, [fetchData]);

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'auto',
      gap: '16px',
      padding: '16px',
      paddingBottom: '80px'
    }}>
      {/* Filter Form Card */}
      <Card
        styles={{
          body: { padding: '16px' }
        }}
        style={{
          flexShrink: 0,
          border: 'none',
          boxShadow: 'none'
        }}
      >
        <KafkaMonitorFilterForm />
      </Card>

      {/* Statistics Card */}
      <Card
        title="Thống kê trạng thái"
        styles={{
          header: {
            backgroundColor: THEME_COLORS.BASE_COLOR,
            color: 'white',
            textAlign: 'left'
          },
          body: { padding: '16px' }
        }}
        style={{
          flexShrink: 0,
          border: 'none',
          boxShadow: 'none'
        }}
      >
        <StatusPieChart
          ref={chartRef}
          applicationId={filterValues?.maUngDung || "014"}
          filterParams={filterValues}
          onStatusClick={handleStatusClick}
          apiType="kafka"
        />
      </Card>

      {/* Table Card */}
      <Card
        styles={{
          body: {
            padding: '16px'
          }
        }}
        style={{
          border: 'none',
          boxShadow: 'none'
        }}
      >
        <KafkaMonitorTable />
      </Card>

      {/* Detail Modal */}
      <KafkaMonitorDetailModal
        visible={isDetailModalVisible}
        recordId={selectedRecordId || undefined}
        maUngDung={filterValues?.maUngDung || "014"}
        onClose={handleCloseModal}
        onResend={handleResendMessage}
        onMarkProcessed={handleMarkAsProcessed}
      />
    </div>
  );
};

export default QuanLyKafkaMonitor;
