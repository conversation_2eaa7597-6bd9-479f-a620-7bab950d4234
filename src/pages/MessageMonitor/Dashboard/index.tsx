import { <PERSON>, Button } from "antd";
import { FC, useState, useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { Row, Col } from 'antd';
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';
import dayjs from 'dayjs';
import 'dayjs/locale/vi'; // Import Vietnamese locale
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { sentStatus, getMessageStatusDisplay } from '@/containers/MessageMonitor/constants';

// Configure dayjs with Vietnamese locale
dayjs.extend(localizedFormat);
dayjs.locale('vi');

import { useDashboardStore } from './useMessageMonitorStore';
import { MessageMonitorFilterForm } from '@/containers/MessageMonitor/MessageMonitorFilterForm';
import MessageMonitorTable from "@/containers/MessageMonitor/MessageMonitorTable";
import MessageMonitorDetailModal from "@/containers/MessageMonitor/MessageMonitorDetailModal";
import MessageMonitorBulkResendModal from "@/containers/MessageMonitor/MessageMonitorBulkResendModal";
import { MessageMonitorItem, MessageMonitorApi, MessageStatisticsRequest } from '@/api/message-monitor';
import { initializeApiToken } from '@/utils/common/storage';
import { THEME_COLORS } from '@/utils/ui';

interface SentChartData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

interface ReceivedChartData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

interface IntervalChartData {
  label: string;
  apiLabel?: string; // For matching with API data
  sentTotal: number;
  receivedTotal: number;
}

// Utility function to calculate optimal interval based on time range
const calculateOptimalInterval = (fromDate: dayjs.Dayjs, toDate: dayjs.Dayjs): string => {
  const diffInMinutes = toDate.diff(fromDate, 'minute');
  const diffInHours = toDate.diff(fromDate, 'hour');
  const diffInDays = toDate.diff(fromDate, 'day');
  
  // Define interval rules based on time range duration
  if (diffInMinutes <= 60) {
    return '5m';        // 1 hour or less: 5-minute intervals
  } else if (diffInHours <= 6) {
    return '15m';       // 6 hours or less: 15-minute intervals
  } else if (diffInHours <= 24) {
    return '1h';        // 1 day or less: 1-hour intervals
  } else if (diffInDays <= 7) {
    return '6h';        // 1 week or less: 6-hour intervals
  } else if (diffInDays <= 30) {
    return '1d';        // 1 month or less: 1-day intervals
  } else if (diffInDays <= 90) {
    return '3d';        // 3 months or less: 3-day intervals
  } else {
    return '1w';        // More than 3 months: 1-week intervals
  }
};

// Utility function to generate complete interval list
const generateCompleteIntervals = (fromDate: dayjs.Dayjs, toDate: dayjs.Dayjs, interval: string): IntervalChartData[] => {
  const intervals: IntervalChartData[] = [];
  let current = fromDate.clone();
  
  // Determine step size and format based on interval
  let stepAmount: number;
  let stepUnit: dayjs.ManipulateType;
  
  // Round the start time to appropriate boundary based on interval
  switch (interval) {
    case '5m':
      stepAmount = 5;
      stepUnit = 'minute';
      // Round to nearest 5 minutes
      current = current.startOf('minute').minute(Math.floor(current.minute() / 5) * 5);
      break;
    case '15m':
      stepAmount = 15;
      stepUnit = 'minute';
      // Round to nearest 15 minutes
      current = current.startOf('minute').minute(Math.floor(current.minute() / 15) * 15);
      break;
    case '1h':
      stepAmount = 1;
      stepUnit = 'hour';
      // Round to nearest hour
      current = current.startOf('hour');
      break;
    case '6h':
      stepAmount = 6;
      stepUnit = 'hour';
      // Round to nearest 6-hour boundary (00:00, 06:00, 12:00, 18:00)
      current = current.startOf('hour').hour(Math.floor(current.hour() / 6) * 6);
      break;
    case '1d':
      stepAmount = 1;
      stepUnit = 'day';
      // Round to start of day
      current = current.startOf('day');
      break;
    case '3d':
      stepAmount = 3;
      stepUnit = 'day';
      // Round to start of day
      current = current.startOf('day');
      break;
    case '1w':
      stepAmount = 1;
      stepUnit = 'week';
      // Round to start of week
      current = current.startOf('week');
      break;
    default:
      stepAmount = 1;
      stepUnit = 'hour';
      current = current.startOf('hour');
  }
  
  // Generate intervals with API-compatible labels and display labels
  while (current.isBefore(toDate) || current.isSame(toDate)) {
    // Create API-compatible label (ISO format like API returns)
    const apiLabel = current.format('YYYY-MM-DDTHH:mm:ss.SSSSSS');
    
    // Create display label (Kibana-style formatting with rounded time)
    const displayLabel = formatDisplayLabel(current, interval, fromDate, toDate);
    
    intervals.push({
      label: displayLabel,
      apiLabel: apiLabel, // Keep API label for matching
      sentTotal: 0,
      receivedTotal: 0
    });
    current = current.add(stepAmount, stepUnit);
  }
  
  return intervals;
};

// Utility function to format display labels like Kibana
const formatDisplayLabel = (date: dayjs.Dayjs, interval: string, fromDate: dayjs.Dayjs, toDate: dayjs.Dayjs): string => {
  const today = dayjs().startOf('day');
  const isToday = fromDate.isSame(today, 'day') && toDate.isSame(today, 'day');
  const isSameDay = fromDate.isSame(toDate, 'day');
  
  switch (interval) {
    case '5m':
    case '15m':
      // For minute intervals: always show "13:30"
      return date.format('HH:mm');
    case '1h':
      // For hourly intervals: if same day or today, show only time, otherwise show day + time
      if (isToday || isSameDay) {
        return date.format('HH:mm');
      }
      return date.format('ddd DD, HH:mm');
    case '6h':
      // For 6-hour intervals: if same day or today, show only time, otherwise show day + time
      if (isToday || isSameDay) {
        return date.format('HH:mm');
      }
      return date.format('ddd DD, HH:mm');
    case '1d':
      // For daily intervals: if within same month, show day only, otherwise show day/month
      if (fromDate.isSame(toDate, 'month')) {
        return date.format('DD');
      }
      return date.format('ddd DD/MM');
    case '3d':
    case '1w':
      // For multi-day intervals: always show day/month
      return date.format('ddd DD/MM');
    default:
      if (isToday || isSameDay) {
        return date.format('HH:mm');
      }
      return date.format('ddd DD, HH:mm');
  }
};

// Utility function to merge API data with complete intervals
const mergeIntervalData = (completeIntervals: IntervalChartData[], apiIntervals: any[]): IntervalChartData[] => {
  console.log('Merging intervals - API data:', apiIntervals);
  console.log('Merging intervals - Complete intervals count:', completeIntervals.length);
  
  const merged = [...completeIntervals];
  
  // For each API interval, find the closest matching generated interval
  apiIntervals.forEach(apiItem => {
    console.log('Processing API item:', apiItem);
    const apiDate = dayjs(apiItem.label);
    
    if (!apiDate.isValid()) {
      console.warn('Invalid API date:', apiItem.label);
      return;
    }
    
    // Find the closest interval in our generated list
    let closestIndex = -1;
    let minDiff = Infinity;
    
    merged.forEach((interval, index) => {
      if (interval.apiLabel) {
        const intervalDate = dayjs(interval.apiLabel);
        if (intervalDate.isValid()) {
          const diff = Math.abs(apiDate.diff(intervalDate, 'minute'));
          if (diff < minDiff) {
            minDiff = diff;
            closestIndex = index;
          }
        }
      }
    });
    
    // If we found a close match (within reasonable tolerance), update it
    if (closestIndex !== -1 && minDiff <= 360) { // 6 hour tolerance for 6h intervals
      console.log(`Matched API data to interval ${closestIndex}, diff: ${minDiff} minutes`);
      merged[closestIndex] = {
        ...merged[closestIndex],
        sentTotal: apiItem.messageCount || 0,
        receivedTotal: apiItem.trackingCount || 0
      };
    } else {
      console.warn(`No close match found for API item ${apiItem.label}, closest diff: ${minDiff} minutes`);
    }
  });
  
  console.log('Final merged intervals:', merged.slice(0, 5)); // Log first 5 for debugging
  return merged;
};

const Dashboard: FC = () => {
  console.log('MessageMonitor Dashboard component loaded');

  // Initialize the API token on component mount
  useEffect(() => {
    initializeApiToken();
  }, []);

  // Local state for table data
  const [tableData, setTableData] = useState<MessageMonitorItem[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  // Local state for charts
  const [chartsLoading, setChartsLoading] = useState(false);
  const [sentChartData, setSentChartData] = useState<SentChartData[]>([]);
  const [receivedChartData, setReceivedChartData] = useState<ReceivedChartData[]>([]);
  const [intervalChartData, setIntervalChartData] = useState<IntervalChartData[]>([]);

  // Get state and actions from the store
  const {
    isDetailModalVisible,
    selectedRecordId,
    isBulkResendModalVisible,
    isFilterLoading,
    selectedRowKeys,
    setDetailModalVisible,
    setSelectedRecordId,
    setBulkResendModalVisible,
    handleResendMessage,
    handleBulkResendWithParams,
    handleBulkResend
  } = useDashboardStore(
    useShallow((state) => ({
      isDetailModalVisible: state.isDetailModalVisible,
      selectedRecordId: state.selectedRecordId,
      isBulkResendModalVisible: state.isBulkResendModalVisible,
      isFilterLoading: state.isFilterLoading,
      selectedRowKeys: state.selectedRowKeys,
      setDetailModalVisible: state.setDetailModalVisible,
      setSelectedRecordId: state.setSelectedRecordId,
      setBulkResendModalVisible: state.setBulkResendModalVisible,
      handleResendMessage: state.handleResendMessage,
      handleBulkResendWithParams: state.handleBulkResendWithParams,
      handleBulkResend: state.handleBulkResend
    }))
  );

  // Get status colors for sent messages
  const getSentStatusColor = (statusName: string): string => {
    const name = statusName.toLowerCase();
    if (name.includes('đã gửi') || name.includes('thành công')) return '#52c41a'; // Green
    if (name.includes('chưa gửi') || name.includes('pending')) return '#faad14'; // Yellow
    if (name.includes('đang gửi') || name.includes('processing')) return '#1890ff'; // Blue
    if (name.includes('gửi lỗi') || name.includes('thất bại')) return '#ff4d4f'; // Red
    return '#722ed1'; // Purple fallback
  };

  // Get status colors for received messages
  const getReceivedStatusColor = (statusName: string): string => {
    const name = statusName.toLowerCase();
    if (name.includes('đã xử lý') || name.includes('thành công')) return '#52c41a'; // Green
    if (name.includes('xử lý lỗi') || name.includes('thất bại')) return '#ff4d4f'; // Red
    return '#722ed1'; // Purple fallback
  };

  // Fetch statistics data with filters
  const fetchStatistics = async (filters?: any) => {
    try {
      setChartsLoading(true);
      
      // Calculate optimal interval based on date range
      let optimizedFilters = { ...filters };
      if (filters?.fromDate && filters?.toDate) {
        const fromDate = dayjs(filters.fromDate);
        const toDate = dayjs(filters.toDate);
        const optimalInterval = calculateOptimalInterval(fromDate, toDate);
        optimizedFilters.interval = optimalInterval;
        
        console.log(`Time range: ${fromDate.format()} to ${toDate.format()}`);
        console.log(`Calculated optimal interval: ${optimalInterval}`);
        
        // Generate complete interval list for the time range
        const completeIntervals = generateCompleteIntervals(fromDate, toDate, optimalInterval);
        console.log(`Generated ${completeIntervals.length} complete intervals`);
      } else {
        // Default interval if no date range provided
        optimizedFilters.interval = '6h';        // Better default for 7 days
      }

      console.log('Fetching statistics with optimized filters:', optimizedFilters);
      const response = await MessageMonitorApi.getStatistics(optimizedFilters);

      if (response.success && response.data) {
        const { sentStats, receivedStats, intervals } = response.data;

        // Process sent statistics for pie chart
        if (sentStats) {
          const sentItems: SentChartData[] = [];
          const total = sentStats.total || 0;

          if (sentStats.pending > 0) {
            sentItems.push({
              name: 'Chưa gửi',
              value: sentStats.pending,
              percentage: total > 0 ? Math.round((sentStats.pending / total) * 100) : 0,
              color: getSentStatusColor('chưa gửi')
            });
          }

          if (sentStats.successful > 0) {
            sentItems.push({
              name: 'Đã gửi',
              value: sentStats.successful,
              percentage: total > 0 ? Math.round((sentStats.successful / total) * 100) : 0,
              color: getSentStatusColor('đã gửi')
            });
          }

          if (sentStats.processing > 0) {
            sentItems.push({
              name: 'Đang gửi',
              value: sentStats.processing,
              percentage: total > 0 ? Math.round((sentStats.processing / total) * 100) : 0,
              color: getSentStatusColor('đang gửi')
            });
          }

          if (sentStats.failed > 0) {
            sentItems.push({
              name: 'Gửi lỗi',
              value: sentStats.failed,
              percentage: total > 0 ? Math.round((sentStats.failed / total) * 100) : 0,
              color: getSentStatusColor('gửi lỗi')
            });
          }

          setSentChartData(sentItems);
        }

        // Process received statistics for pie chart
        if (receivedStats) {
          const receivedItems: ReceivedChartData[] = [];
          const total = receivedStats.total || 0;

          if (receivedStats.successful > 0) {
            receivedItems.push({
              name: 'Đã xử lý',
              value: receivedStats.successful,
              percentage: total > 0 ? Math.round((receivedStats.successful / total) * 100) : 0,
              color: getReceivedStatusColor('đã xử lý')
            });
          }

          if (receivedStats.failed > 0) {
            receivedItems.push({
              name: 'Xử lý lỗi',
              value: receivedStats.failed,
              percentage: total > 0 ? Math.round((receivedStats.failed / total) * 100) : 0,
              color: getReceivedStatusColor('xử lý lỗi')
            });
          }

          setReceivedChartData(receivedItems);
        }

        // Process interval statistics with complete time series
        if (optimizedFilters?.fromDate && optimizedFilters?.toDate && optimizedFilters?.interval) {
          const fromDate = dayjs(optimizedFilters.fromDate);
          const toDate = dayjs(optimizedFilters.toDate);
          
          // Generate complete interval list
          const completeIntervals = generateCompleteIntervals(fromDate, toDate, optimizedFilters.interval);
          
          // Merge with API data
          const mergedIntervals = mergeIntervalData(completeIntervals, intervals || []);
          
          console.log(`API returned ${intervals?.length || 0} intervals, showing ${mergedIntervals.length} complete intervals`);
          setIntervalChartData(mergedIntervals);
        } else if (intervals && intervals.length > 0) {
          // Fallback to API data only if no date range
          const intervalItems: IntervalChartData[] = intervals.map(interval => ({
            label: interval.label,
            sentTotal: interval.messageCount || 0,
            receivedTotal: interval.trackingCount || 0
          }));
          setIntervalChartData(intervalItems);
        } else {
          setIntervalChartData([]);
        }
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    } finally {
      setChartsLoading(false);
    }
  };

  // Load chart data on component mount with 7 days default
  useEffect(() => {
    // Default to 7 days range for statistics
    const defaultFilters = {
      interval: '1h',
      fromDate: dayjs().subtract(7, 'day').format('YYYY-MM-DDTHH:mm:ss'),
      toDate: dayjs().format('YYYY-MM-DDTHH:mm:ss')
    };
    fetchStatistics(defaultFilters);
  }, []);

  // Handle data loaded from filter form
  const handleDataLoaded = (data: MessageMonitorItem[], total: number) => {
    console.log('Dashboard received data from filter form:', { data, total });
    setTableData(data);
    setTotalRecords(total);
  };

  // Handle closing the detail modal
  const handleCloseModal = () => {
    setDetailModalVisible(false);
    setSelectedRecordId(null);
  };

  // Handle closing the bulk resend modal
  const handleCloseBulkResendModal = () => {
    setBulkResendModalVisible(false);
  };

  // Handle bulk resend button click
  const handleBulkResendClick = () => {
    if (selectedRowKeys.length > 0) {
      handleBulkResend();
    } else {
      setBulkResendModalVisible(true);
    }
  };

  // Handle statistics refresh from filter form
  const handleStatisticsLoaded = (filters: any) => {
    console.log('Dashboard refreshing statistics with filters:', filters);
    fetchStatistics(filters);
  };

  // Custom tooltip for pie charts
  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: 'white',
          padding: '8px 12px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>{data.name}</p>
          <p style={{ margin: 0, color: data.color }}>
            Số lượng: {data.value.toLocaleString()}
          </p>
          <p style={{ margin: 0, color: data.color }}>
            Tỷ lệ: {data.percentage}%
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom tooltip for bar charts
  const CustomBarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          padding: '8px 12px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ margin: 0, color: entry.color }}>
              {entry.name}: {entry.value.toLocaleString()}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Custom legend component for pie charts (Grafana-style)
  const CustomPieLegend = ({ payload }: any) => {
    if (!payload || payload.length === 0) return null;

    return (
      <div style={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: '6px 12px',
        justifyContent: 'flex-start',
        alignItems: 'center',
        padding: '4px 0',
        fontSize: '11px'
      }}>
        {payload.map((entry: any, index: number) => {
          // Get proper status name using the status value
          const statusName = entry.name || getMessageStatusDisplay(entry.value) || entry.value;

          return (
            <div
              key={`legend-${index}`}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                whiteSpace: 'nowrap'
              }}
            >
              <div
                style={{
                  width: '12px',
                  height: '12px',
                  backgroundColor: entry.color,
                  borderRadius: '2px',
                  flexShrink: 0
                }}
              />
              <span style={{ color: '#333', fontWeight: 500 }}>
                {statusName}: {entry.percentage || 0}%
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'auto',
      gap: '16px',
      padding: '16px',
      paddingBottom: '80px'
    }}>
      {/* Charts Section */}
      <Row gutter={[16, 16]} style={{ flexShrink: 0 }}>
        {/* Sent Messages Pie Chart */}
        <Col span={6}>
          <Card
            title="Bản tin gửi"
            loading={chartsLoading}
            style={{ height: 320 }}
            styles={{
              header: {
                backgroundColor: THEME_COLORS.BASE_COLOR,
                color: 'white',
                minHeight: '40px',
                padding: '8px 16px'
              },
              body: { padding: '16px' }
            }}
          >
            <div style={{ height: 250, display: 'flex', flexDirection: 'column' }}>
              {sentChartData.length > 0 ? (
                <>
                  <div style={{ flex: 1 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={sentChartData}
                          cx="50%"
                          cy="50%"
                          outerRadius={75}
                          dataKey="value"
                          stroke="none"
                        >
                          {sentChartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomPieTooltip />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div style={{ height: 'auto', minHeight: '30px', maxHeight: '50px', overflow: 'hidden' }}>
                    <CustomPieLegend payload={sentChartData} />
                  </div>
                </>
              ) : (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                  color: '#999',
                  fontSize: '12px'
                }}>
                  Không có dữ liệu
                </div>
              )}
            </div>
          </Card>
        </Col>

        {/* Received Messages Pie Chart */}
        <Col span={6}>
          <Card
            title="Bản tin nhận xử lý"
            loading={chartsLoading}
            style={{ height: 320 }}
            styles={{
              header: {
                backgroundColor: THEME_COLORS.BASE_COLOR,
                color: 'white',
                minHeight: '40px',
                padding: '8px 16px'
              },
              body: { padding: '16px' }
            }}
          >
            <div style={{ height: 250, display: 'flex', flexDirection: 'column' }}>
              {receivedChartData.length > 0 ? (
                <>
                  <div style={{ flex: 1 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={receivedChartData}
                          cx="50%"
                          cy="50%"
                          outerRadius={75}
                          dataKey="value"
                          stroke="none"
                        >
                          {receivedChartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomPieTooltip />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div style={{ height: 'auto', minHeight: '30px', maxHeight: '50px', overflow: 'hidden' }}>
                    <CustomPieLegend payload={receivedChartData} />
                  </div>
                </>
              ) : (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                  color: '#999',
                  fontSize: '12px'
                }}>
                  Không có dữ liệu
                </div>
              )}
            </div>
          </Card>
        </Col>

        {/* Combined Time-based Chart */}
        <Col span={12}>
          <Card
            title="Số lượng theo thời gian"
            loading={chartsLoading}
            style={{ height: 320 }}
            styles={{
              header: {
                backgroundColor: THEME_COLORS.BASE_COLOR,
                color: 'white',
                minHeight: '40px',
                padding: '8px 16px'
              },
              body: { padding: '16px' }
            }}
          >
            <div style={{ height: 250 }}>
              {intervalChartData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={intervalChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="label"
                      tick={{ fontSize: 10 }}
                      angle={-45}
                      textAnchor="end"
                      height={60}
                    />
                    <YAxis tick={{ fontSize: 10 }} />
                    <Tooltip content={<CustomBarTooltip />} />
                    <Bar
                      dataKey="sentTotal"
                      stackId="a"
                      name="Số lượng bản tin message"
                      fill="#1890ff"
                    />
                    <Bar
                      dataKey="receivedTotal"
                      stackId="a"
                      name="Số lượng bản tin tracking"
                      fill="#52c41a"
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                  color: '#999',
                  fontSize: '12px'
                }}>
                  Không có dữ liệu
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Filter Form Card */}
      <Card
        styles={{
          body: { padding: '16px' }
        }}
        style={{
          flexShrink: 0,
          border: 'none',
          boxShadow: 'none'
        }}
      >
        <MessageMonitorFilterForm
          onDataLoaded={handleDataLoaded}
          onStatisticsLoaded={handleStatisticsLoaded}
          selectedRowKeys={selectedRowKeys}
          onResendAll={handleBulkResendClick}
        />
      </Card>

      {/* Table Card */}
      <Card
        styles={{
          body: {
            padding: '16px'
          }
        }}
        style={{
          border: 'none',
          boxShadow: 'none'
        }}
      >
        {/* Selection Info Bar */}
        {selectedRowKeys.length > 0 && (
          <div style={{
            backgroundColor: '#e6f7ff',
            border: '1px solid #91d5ff',
            borderRadius: '6px',
            padding: '12px 16px',
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <span style={{ 
              color: '#0958d9',
              fontWeight: 500,
              fontSize: '14px'
            }}>
              Bạn đã chọn {selectedRowKeys.length} bản ghi
            </span>
            <Button
              type="text"
              size="small"
              onClick={() => {
                const { setSelectedRowKeys } = useDashboardStore.getState();
                setSelectedRowKeys([]);
              }}
              style={{
                color: '#0958d9',
                padding: '4px 8px'
              }}
            >
              Bỏ chọn tất cả
            </Button>
          </div>
        )}
        
        <MessageMonitorTable
          dataSource={tableData}
          total={totalRecords}
        />
      </Card>

      {/* Detail Modal */}
      <MessageMonitorDetailModal
        visible={isDetailModalVisible}
        recordId={selectedRecordId || undefined}
        onClose={handleCloseModal}
        onResend={handleResendMessage}
      />

      {/* Bulk Resend Modal */}
      <MessageMonitorBulkResendModal
        visible={isBulkResendModalVisible}
        onClose={handleCloseBulkResendModal}
        onSubmit={handleBulkResendWithParams}
        loading={isFilterLoading}
      />
    </div>
  );
};

export default Dashboard;
