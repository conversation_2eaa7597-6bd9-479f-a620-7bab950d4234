import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, message, Modal, DatePicker, Select } from 'antd';
import { ReloadOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons';
import { MessageMonitorApi, ReplayRequest, SearchReplayRequest } from '@/api/message-monitor';
import { THEME_COLORS } from '@/utils/ui';
import { useProducerOptions } from '@/hooks/useProducerOptions';
import { initializeApiToken } from '@/utils/common/storage';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

const ReplayRequestManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [requests, setRequests] = useState<ReplayRequest[]>([]);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  const [filters, setFilters] = useState<SearchReplayRequest>({});
  const [selectedRequest, setSelectedRequest] = useState<ReplayRequest | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  const { options: producerOptions, loading: producerLoading } = useProducerOptions();

  // Initialize API token on component mount
  useEffect(() => {
    console.log('ReplayRequestManagement: Initializing API token...');
    initializeApiToken();
  }, []);

  // Fetch replay requests
  const fetchRequests = async () => {
    try {
      console.log('ReplayRequestManagement: Starting fetchRequests...');
      setLoading(true);
      
      const params = {
        ...filters,
        page: pagination.current - 1,
        size: pagination.pageSize,
      };

      console.log('ReplayRequestManagement: API params:', params);
      
      const response = await MessageMonitorApi.searchReplayRequests(params);
      
      console.log('ReplayRequestManagement: API response:', response);
      
      if (response.success && response.data) {
        console.log('ReplayRequestManagement: Setting data:', response.data.content?.length || 0, 'items');
        setRequests(response.data.content || []);
        setTotal(response.data.totalElements || 0);
      } else {
        console.error('ReplayRequestManagement: API returned unsuccessful response:', response);
        message.error(response.message || 'Không thể tải dữ liệu');
      }
    } catch (error) {
      console.error('ReplayRequestManagement: Error fetching replay requests:', error);
      
      // More detailed error logging
      if (error instanceof Error) {
        console.error('ReplayRequestManagement: Error message:', error.message);
        console.error('ReplayRequestManagement: Error stack:', error.stack);
      }
      
      message.error('Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    console.log('ReplayRequestManagement: Component mounted, calling fetchRequests...');
    fetchRequests();
  }, []); // Empty dependency array - only run on mount

  // Handle pagination changes separately
  useEffect(() => {
    if (pagination.current > 1 || pagination.pageSize !== 10) {
      console.log('ReplayRequestManagement: Pagination changed, calling fetchRequests...');
      fetchRequests();
    }
  }, [pagination.current, pagination.pageSize]);

  // Handle view detail
  const handleViewDetail = async (request: ReplayRequest) => {
    try {
      const response = await MessageMonitorApi.getReplayRequestById(request.id);
      if (response.success && response.data) {
        setSelectedRequest(response.data);
        setDetailModalVisible(true);
      }
    } catch (error) {
      console.error('Error fetching request detail:', error);
      message.error('Không thể tải chi tiết yêu cầu');
    }
  };

  // Handle table change
  const handleTableChange = (newPagination: any) => {
    setPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  // Handle search
  const handleSearch = () => {
    console.log('handleSearch called');
    setPagination(prev => ({ ...prev, current: 1 }));
    // Call fetchRequests immediately after setting pagination
    fetchRequests();
  };

  // Handle reset filters
  const handleReset = () => {
    console.log('handleReset called');
    setFilters({});
    setPagination({ current: 1, pageSize: 10 });
    // Call fetchRequests immediately after resetting
    fetchRequests();
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'RUNNING':
        return 'processing';
      case 'SCHEDULED':
        return 'default';
      case 'FAILED':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'Hoàn thành';
      case 'RUNNING':
        return 'Đang chạy';
      case 'SCHEDULED':
        return 'Đã lên lịch';
      case 'FAILED':
        return 'Thất bại';
      default:
        return status;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      render: (text: string) => (
        <span style={{
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '11px'
        }}>
          {text}
        </span>
      ),
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={type === 'RANGE' ? 'blue' : 'green'}>
          {type === 'RANGE' ? 'Khoảng thời gian' : 'Danh sách ID'}
        </Tag>
      ),
    },
    {
      title: 'Ứng dụng',
      dataIndex: 'appCode',
      key: 'appCode',
      width: 150,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'Tổng số tin',
      dataIndex: 'totalMessages',
      key: 'totalMessages',
      width: 100,
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: 'Đã xử lý',
      dataIndex: 'processedMessages',
      key: 'processedMessages',
      width: 100,
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: 'Tiến độ',
      key: 'progress',
      width: 100,
      align: 'center' as const,
      render: (_: any, record: ReplayRequest) => {
        const progress = record.totalMessages && record.totalMessages > 0 
          ? Math.round((record.processedMessages || 0) / record.totalMessages * 100)
          : 0;
        return `${progress}%`;
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdTime',
      key: 'createdTime',
      width: 160,
      render: (value: string) => value ? dayjs(value).format('DD/MM/YYYY HH:mm:ss') : '-',
    },
    {
      title: 'Cập nhật cuối',
      dataIndex: 'updatedTime',
      key: 'updatedTime',
      width: 160,
      render: (value: string) => value ? dayjs(value).format('DD/MM/YYYY HH:mm:ss') : '-',
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 120,
      align: 'center' as const,
      render: (_: any, record: ReplayRequest) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
            size="small"
          >
            Chi tiết
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '16px' }}>
      {/* Filter Section */}
      <Card style={{ marginBottom: 16 }} title="Yêu cầu gửi lại">
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Space wrap>
            <Select
              style={{ width: 200 }}
              placeholder="Loại yêu cầu"
              value={filters.type}
              onChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
              allowClear
            >
              <Option value="RANGE">Khoảng thời gian</Option>
              <Option value="IDS">Danh sách ID</Option>
            </Select>
            
            <Select
              style={{ width: 200 }}
              placeholder="Ứng dụng"
              value={filters.appCode}
              onChange={(value) => setFilters(prev => ({ ...prev, appCode: value }))}
              allowClear
              loading={producerLoading}
            >
              {producerOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>

            <Select
              style={{ width: 150 }}
              placeholder="Trạng thái"
              value={filters.status}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              allowClear
            >
              <Option value="SCHEDULED">Đã lên lịch</Option>
              <Option value="RUNNING">Đang chạy</Option>
              <Option value="COMPLETED">Hoàn thành</Option>
              <Option value="FAILED">Thất bại</Option>
            </Select>

            <RangePicker
              style={{ width: 300 }}
              format="DD/MM/YYYY HH:mm"
              showTime
              onChange={(dates) => {
                if (dates && dates[0] && dates[1]) {
                  setFilters(prev => ({
                    ...prev,
                    fromDate: dates[0]!.toISOString(),
                    toDate: dates[1]!.toISOString(),
                  }));
                } else {
                  setFilters(prev => {
                    const { fromDate, toDate, ...rest } = prev;
                    return rest;
                  });
                }
              }}
            />
          </Space>

          <Space>
            <Button
              type="primary"
              onClick={handleSearch}
              loading={loading}
              style={{
                backgroundColor: THEME_COLORS.BASE_COLOR,
                borderColor: THEME_COLORS.BASE_COLOR,
              }}
            >
              Tìm kiếm
            </Button>
            <Button onClick={handleReset}>
              Reset
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchRequests}
              loading={loading}
            >
              Làm mới
            </Button>
          </Space>
        </Space>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={requests}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} yêu cầu`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* Detail Modal */}
      <Modal
        title="Chi tiết yêu cầu gửi lại"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedRequest(null);
        }}
        footer={[
          <Button
            key="close"
            onClick={() => {
              setDetailModalVisible(false);
              setSelectedRequest(null);
            }}
          >
            Đóng
          </Button>
        ]}
        width={800}
      >
        {selectedRequest && (
          <div>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <div>
                <strong>ID:</strong> {selectedRequest.id}
              </div>
              <div>
                <strong>Loại:</strong> {selectedRequest.type === 'RANGE' ? 'Khoảng thời gian' : 'Danh sách ID'}
              </div>
              <div>
                <strong>Ứng dụng:</strong> {selectedRequest.appCode}
              </div>
              <div>
                <strong>Trạng thái:</strong>{' '}
                <Tag color={getStatusColor(selectedRequest.status)}>
                  {getStatusText(selectedRequest.status)}
                </Tag>
              </div>
              {selectedRequest.description && (
                <div>
                  <strong>Mô tả:</strong> {selectedRequest.description}
                </div>
              )}
              {selectedRequest.type === 'RANGE' && (
                <>
                  <div>
                    <strong>Từ ngày:</strong> {selectedRequest.fromDate ? dayjs(selectedRequest.fromDate).format('DD/MM/YYYY HH:mm:ss') : '-'}
                  </div>
                  <div>
                    <strong>Đến ngày:</strong> {selectedRequest.toDate ? dayjs(selectedRequest.toDate).format('DD/MM/YYYY HH:mm:ss') : '-'}
                  </div>
                </>
              )}
              {selectedRequest.type === 'IDS' && selectedRequest.messageIds && Array.isArray(selectedRequest.messageIds) && selectedRequest.messageIds.length > 0 && (
                <div>
                  <strong>Danh sách Message IDs ({selectedRequest.messageIds.length}):</strong>
                  <div style={{
                    maxHeight: 200,
                    overflowY: 'auto',
                    backgroundColor: '#f5f5f5',
                    padding: 8,
                    borderRadius: 4,
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    fontSize: '11px'
                  }}>
                    {selectedRequest.messageIds.map((id, index) => (
                      <div key={index}>{id}</div>
                    ))}
                  </div>
                </div>
              )}
              <div>
                <strong>Tổng số tin:</strong> {selectedRequest.totalMessages || 0}
              </div>
              <div>
                <strong>Đã xử lý:</strong> {selectedRequest.processedMessages || 0}
              </div>
              <div>
                <strong>Người tạo:</strong> {selectedRequest.createdBy || '-'}
              </div>
              <div>
                <strong>Ngày tạo:</strong> {selectedRequest.createdTime ? dayjs(selectedRequest.createdTime).format('DD/MM/YYYY HH:mm:ss') : '-'}
              </div>
              <div>
                <strong>Cập nhật cuối:</strong> {selectedRequest.updatedTime ? dayjs(selectedRequest.updatedTime).format('DD/MM/YYYY HH:mm:ss') : '-'}
              </div>
            </Space>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ReplayRequestManagement;