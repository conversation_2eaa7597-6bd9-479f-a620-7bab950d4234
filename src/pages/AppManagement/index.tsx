import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, message, Modal, Select } from 'antd';
import { ReloadOutlined, SettingOutlined } from '@ant-design/icons';
import { AppManagementApi, AppInfo, AppStatus, JobType } from '@/api/app-management';
import { THEME_COLORS } from '@/utils/ui';
import dayjs from 'dayjs';

interface AppWithStatus extends AppInfo {
  status?: AppStatus;
}

const AppManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [apps, setApps] = useState<AppWithStatus[]>([]);
  const [resetModalVisible, setResetModalVisible] = useState(false);
  const [selectedApp, setSelectedApp] = useState<string>('');
  const [selectedJobType, setSelectedJobType] = useState<JobType | 'ALL'>('ALL');

  // Fetch apps and their statuses
  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch apps and statuses in parallel
      const [appsResponse, statusesResponse] = await Promise.all([
        AppManagementApi.getAllApps(),
        AppManagementApi.getAllAppSyncStatuses()
      ]);

      if (appsResponse.success && statusesResponse.success) {
        const appsData = appsResponse.data || [];
        const statusesData = statusesResponse.data || [];

        // Combine apps with their statuses
        const appsWithStatus: AppWithStatus[] = appsData.map(app => ({
          ...app,
          status: statusesData.find(status => status.appCode === app.code)
        }));

        setApps(appsWithStatus);
      } else {
        message.error('Không thể tải dữ liệu ứng dụng');
      }
    } catch (error) {
      console.error('Error fetching app data:', error);
      message.error('Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Handle reset last runtime
  const handleReset = async () => {
    try {
      if (!selectedApp) {
        message.error('Vui lòng chọn ứng dụng');
        return;
      }

      setLoading(true);

      if (selectedJobType === 'ALL') {
        await AppManagementApi.resetAllLastRuntimeForApp(selectedApp);
        message.success('Reset tất cả job types thành công');
      } else {
        await AppManagementApi.resetLastRuntime(selectedApp, selectedJobType as JobType);
        message.success(`Reset job type ${selectedJobType} thành công`);
      }

      setResetModalVisible(false);
      setSelectedApp('');
      setSelectedJobType('ALL');
      
      // Refresh data
      await fetchData();
    } catch (error) {
      console.error('Error resetting runtime:', error);
      message.error('Có lỗi xảy ra khi reset');
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return dayjs(dateString).format('DD/MM/YYYY HH:mm:ss');
  };

  // Get status color
  const getStatusColor = (lastTime?: string) => {
    if (!lastTime) return 'default';
    const now = dayjs();
    const last = dayjs(lastTime);
    const diffHours = now.diff(last, 'hour');
    
    if (diffHours < 1) return 'success';
    if (diffHours < 24) return 'warning';
    return 'error';
  };

  const columns = [
    {
      title: 'Mã ứng dụng',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (text: string) => (
        <span style={{ fontWeight: 500 }}>
          {text}
        </span>
      ),
    },
    {
      title: 'Tên ứng dụng',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text?: string) => text || '-',
    },
    {
      title: 'Lần gửi cuối',
      key: 'lastSentTime',
      width: 160,
      render: (_: any, record: AppWithStatus) => (
        <div>
          <Tag color={getStatusColor(record.status?.lastSentTime)}>
            {formatDate(record.status?.lastSentTime)}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Lần đồng bộ cuối',
      key: 'lastSyncTime',
      width: 160,
      render: (_: any, record: AppWithStatus) => (
        <div>
          <Tag color={getStatusColor(record.status?.lastSyncTime)}>
            {formatDate(record.status?.lastSyncTime)}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Đồng bộ tracking cuối',
      key: 'lastTrackingSyncTime',
      width: 160,
      render: (_: any, record: AppWithStatus) => (
        <div>
          <Tag color={getStatusColor(record.status?.lastTrackingSyncTime)}>
            {formatDate(record.status?.lastTrackingSyncTime)}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 120,
      align: 'center' as const,
      render: (_: any, record: AppWithStatus) => (
        <Button
          type="link"
          icon={<SettingOutlined />}
          onClick={() => {
            setSelectedApp(record.code);
            setResetModalVisible(true);
          }}
        >
          Reset
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '16px' }}>
      <Card
        title="Quản lý ứng dụng"
        extra={
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={fetchData}
            loading={loading}
            style={{
              backgroundColor: THEME_COLORS.BASE_COLOR,
              borderColor: THEME_COLORS.BASE_COLOR,
            }}
          >
            Làm mới
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={apps}
          rowKey="code"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} ứng dụng`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Reset Modal */}
      <Modal
        title="Reset Last Runtime"
        open={resetModalVisible}
        onOk={handleReset}
        onCancel={() => {
          setResetModalVisible(false);
          setSelectedApp('');
          setSelectedJobType('ALL');
        }}
        confirmLoading={loading}
        okText="Reset"
        cancelText="Hủy"
        okButtonProps={{
          style: {
            backgroundColor: THEME_COLORS.BASE_COLOR,
            borderColor: THEME_COLORS.BASE_COLOR,
          }
        }}
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
            Ứng dụng:
          </label>
          <Select
            style={{ width: '100%' }}
            value={selectedApp}
            onChange={setSelectedApp}
            placeholder="Chọn ứng dụng"
          >
            {apps.map(app => (
              <Select.Option key={app.code} value={app.code}>
                {app.name} ({app.code})
              </Select.Option>
            ))}
          </Select>
        </div>
        
        <div>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
            Job Type:
          </label>
          <Select
            style={{ width: '100%' }}
            value={selectedJobType}
            onChange={setSelectedJobType}
          >
            <Select.Option value="ALL">Tất cả Job Types</Select.Option>
            <Select.Option value="SENT">SENT</Select.Option>
            <Select.Option value="SYNC_MESSAGE">SYNC_MESSAGE</Select.Option>
            <Select.Option value="SYNC_TRACKING">SYNC_TRACKING</Select.Option>
          </Select>
        </div>
      </Modal>
    </div>
  );
};

export default AppManagement;