import { Form, Input, InputProps } from 'antd';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

interface FloatingTextFieldProps extends Omit<InputProps, 'name'> {
  name: string;
  label?: string;
  required?: boolean;
  className?: string;
  formItemProps?: React.ComponentProps<typeof Form.Item>;
  floatingLabel?: string;
}

export const FloatingTextField: React.FC<FloatingTextFieldProps> = ({
  name,
  label,
  placeholder,
  disabled = false,
  required = false,
  className,
  type = 'text',
  formItemProps,
  floatingLabel,
  ...restProps
}) => {
  const { control, formState: { errors } } = useFormContext();
  const error = errors[name];

  // Get the error message from the error object
  const errorMessage = error?.message as string;

  // If formItemProps contains error status or message, prioritize those (for server-side errors)
  const validateStatus = formItemProps?.validateStatus || (error ? 'error' : undefined);
  const helpMessage = formItemProps?.help || errorMessage;

  // For hidden inputs, render only the input without Form.Item wrapper to avoid taking space
  if (type === 'hidden') {
    return (
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Input
            {...field}
            {...restProps}
            type="hidden"
            style={{ display: 'none' }}
          />
        )}
      />
    );
  }

  const displayLabel = floatingLabel || placeholder || '';

  return (
    <Form.Item
      label={label}
      required={required}
      validateStatus={validateStatus}
      help={helpMessage}
      className={`static-label-container ${className || ''}`}
      {...(formItemProps && { ...formItemProps, validateStatus, help: helpMessage })}
    >
      <div className="static-input">
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <>
              <Input
                {...field}
                {...restProps}
                placeholder=""
                disabled={disabled}
                type={type}
                status={error ? 'error' : undefined}
                onChange={(e) => {
                  field.onChange(e);
                  // Call the original onChange if provided
                  if (restProps.onChange) {
                    restProps.onChange(e);
                  }
                }}
              />
              {displayLabel && (
                <span className="static-label">
                  {displayLabel}
                </span>
              )}
            </>
          )}
        />
      </div>
    </Form.Item>
  );
};
