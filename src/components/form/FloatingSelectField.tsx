import { Form, Select, SelectProps } from 'antd';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

interface Option {
  label: string;
  value: string | number;
  disabled?: boolean;
}

interface FloatingSelectFieldProps extends Omit<SelectProps, 'name' | 'options'> {
  name: string;
  label?: string;
  options: Option[];
  required?: boolean;
  className?: string;
  formItemProps?: React.ComponentProps<typeof Form.Item>;
  floatingLabel?: string;
}

export const FloatingSelectField: React.FC<FloatingSelectFieldProps> = ({
  name,
  label,
  placeholder,
  options,
  disabled = false,
  required = false,
  className,
  mode,
  showSearch = false,
  formItemProps,
  floatingLabel,
  ...restProps
}) => {
  const { control, formState: { errors } } = useFormContext();
  const error = errors[name];

  const displayLabel = floatingLabel || placeholder || '';

  return (
    <Form.Item
      label={displayLabel}
      required={required}
      validateStatus={error ? 'error' : undefined}
      help={error ? (error.message as string) : undefined}
      className={`select-field-container ${className || ''}`}
      {...formItemProps}
    >
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            {...restProps}
            placeholder=""
            disabled={disabled}
            options={options}
            mode={mode}
            showSearch={showSearch}
            status={error ? 'error' : undefined}
            onChange={(value) => {
              field.onChange(value);
            }}
            filterOption={(input, option) =>
              (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase())
            }
          />
        )}
      />
    </Form.Item>
  );
};
