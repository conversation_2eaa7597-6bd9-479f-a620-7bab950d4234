/* Grafana Time Range Picker Styles - Application Theme */
.grafana-time-picker {
  position: relative;
  display: inline-block;
}

.grafana-time-picker-button {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
  font-size: 13px;
  font-weight: 400;
  transition: all 0.2s ease;
}

.grafana-time-picker-button:hover {
  background-color: #f5f5f5 !important;
  border-color: #1b524f !important;
}

.grafana-time-picker-button:focus {
  background-color: #f5f5f5 !important;
  border-color: #1b524f !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

.grafana-time-picker-dropdown {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.grafana-search-input {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
}

.grafana-search-input::placeholder {
  color: #8c8c8c !important;
}

.grafana-search-input:hover {
  border-color: #1b524f !important;
}

.grafana-search-input:focus {
  border-color: #1b524f !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

.grafana-option-item:hover {
  background-color: #f5f5f5 !important;
  color: #1b524f !important;
}

.grafana-option-item.selected {
  background-color: #e6f7ff !important;
  color: #1b524f !important;
  border-left: 2px solid #1b524f !important;
}

/* Custom scrollbar for options list */
.grafana-time-picker-dropdown ::-webkit-scrollbar {
  width: 6px;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-thumb:hover {
  background: #1b524f;
}

/* Override Ant Design Input styles for light theme */
.grafana-time-picker-dropdown .ant-input {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-input:hover {
  border-color: #1b524f !important;
}

.grafana-time-picker-dropdown .ant-input:focus {
  border-color: #1b524f !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

.grafana-time-picker-dropdown .ant-input::placeholder {
  color: #8c8c8c !important;
}

/* Override Ant Design Button styles */
.grafana-time-picker-dropdown .ant-btn-primary {
  background-color: #1b524f !important;
  border-color: #1b524f !important;
  color: #ffffff !important;
  font-weight: 500;
}

.grafana-time-picker-dropdown .ant-btn-primary:hover {
  background-color: #164340 !important;
  border-color: #164340 !important;
}

.grafana-time-picker-dropdown .ant-btn-primary:focus {
  background-color: #164340 !important;
  border-color: #164340 !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

/* Override Ant Design Divider styles */
.grafana-time-picker-dropdown .ant-divider-horizontal {
  border-color: #d9d9d9 !important;
  margin: 16px 0 !important;
}

/* Date Range Picker styles */
.grafana-time-picker-dropdown .ant-picker {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-picker:hover {
  border-color: #1b524f !important;
}

.grafana-time-picker-dropdown .ant-picker:focus {
  border-color: #1b524f !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

.grafana-time-picker-dropdown .ant-picker-input > input {
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-picker-input > input::placeholder {
  color: #8c8c8c !important;
}

/* Date picker button styles */
.grafana-time-picker-dropdown .ant-btn {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-btn:hover {
  background-color: #f5f5f5 !important;
  border-color: #1b524f !important;
  color: #1b524f !important;
}

/* Animation for dropdown */
.grafana-time-picker-dropdown {
  animation: grafanaFadeIn 0.2s ease-out;
}

@keyframes grafanaFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grafana-time-picker-dropdown {
    width: 100vw;
    left: -12px !important;
    right: -12px !important;
    max-width: none;
  }
  
  .grafana-time-picker-button {
    width: 100% !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .grafana-time-picker-button {
    border-width: 2px !important;
  }
  
  .grafana-time-picker-dropdown {
    border-width: 2px !important;
  }
  
  .grafana-option-item.selected {
    border-left-width: 3px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .grafana-time-picker-button,
  .grafana-option-item,
  .grafana-search-input {
    transition: none !important;
  }
  
  .grafana-time-picker-dropdown {
    animation: none !important;
  }
}
