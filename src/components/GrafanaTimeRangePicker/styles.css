/* Grafana Time Range Picker Styles */
.grafana-time-picker {
  position: relative;
  display: inline-block;
}

.grafana-time-picker-button {
  background-color: #1f1f23 !important;
  border-color: #3c3c41 !important;
  color: #ffffff !important;
  font-size: 13px;
  font-weight: 400;
  transition: all 0.2s ease;
}

.grafana-time-picker-button:hover {
  background-color: #2a2a2e !important;
  border-color: #58a6ff !important;
}

.grafana-time-picker-button:focus {
  background-color: #2a2a2e !important;
  border-color: #58a6ff !important;
  box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2) !important;
}

.grafana-time-picker-dropdown {
  background-color: #1f1f23;
  border: 1px solid #3c3c41;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
}

.grafana-search-input {
  background-color: #2a2a2e !important;
  border-color: #3c3c41 !important;
  color: #ffffff !important;
}

.grafana-search-input::placeholder {
  color: #8e8e92 !important;
}

.grafana-search-input:hover {
  border-color: #58a6ff !important;
}

.grafana-search-input:focus {
  border-color: #58a6ff !important;
  box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2) !important;
}

.grafana-option-item:hover {
  background-color: #2a2a2e !important;
  color: #58a6ff !important;
}

.grafana-option-item.selected {
  background-color: #0d1117 !important;
  color: #58a6ff !important;
  border-left: 2px solid #58a6ff !important;
}

/* Custom scrollbar for options list */
.grafana-time-picker-dropdown ::-webkit-scrollbar {
  width: 6px;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-track {
  background: #1f1f23;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-thumb {
  background: #3c3c41;
  border-radius: 3px;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-thumb:hover {
  background: #58a6ff;
}

/* Override Ant Design Input styles in dark theme */
.grafana-time-picker-dropdown .ant-input {
  background-color: #2a2a2e !important;
  border-color: #3c3c41 !important;
  color: #ffffff !important;
}

.grafana-time-picker-dropdown .ant-input:hover {
  border-color: #58a6ff !important;
}

.grafana-time-picker-dropdown .ant-input:focus {
  border-color: #58a6ff !important;
  box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2) !important;
}

.grafana-time-picker-dropdown .ant-input::placeholder {
  color: #8e8e92 !important;
}

/* Override Ant Design Button styles */
.grafana-time-picker-dropdown .ant-btn-primary {
  background-color: #0969da !important;
  border-color: #0969da !important;
  color: #ffffff !important;
  font-weight: 500;
}

.grafana-time-picker-dropdown .ant-btn-primary:hover {
  background-color: #0860ca !important;
  border-color: #0860ca !important;
}

.grafana-time-picker-dropdown .ant-btn-primary:focus {
  background-color: #0860ca !important;
  border-color: #0860ca !important;
  box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.2) !important;
}

/* Override Ant Design Divider styles */
.grafana-time-picker-dropdown .ant-divider-horizontal {
  border-color: #3c3c41 !important;
  margin: 16px 0 !important;
}

/* Animation for dropdown */
.grafana-time-picker-dropdown {
  animation: grafanaFadeIn 0.2s ease-out;
}

@keyframes grafanaFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grafana-time-picker-dropdown {
    width: 100vw;
    left: -12px !important;
    right: -12px !important;
    max-width: none;
  }
  
  .grafana-time-picker-button {
    width: 100% !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .grafana-time-picker-button {
    border-width: 2px !important;
  }
  
  .grafana-time-picker-dropdown {
    border-width: 2px !important;
  }
  
  .grafana-option-item.selected {
    border-left-width: 3px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .grafana-time-picker-button,
  .grafana-option-item,
  .grafana-search-input {
    transition: none !important;
  }
  
  .grafana-time-picker-dropdown {
    animation: none !important;
  }
}
