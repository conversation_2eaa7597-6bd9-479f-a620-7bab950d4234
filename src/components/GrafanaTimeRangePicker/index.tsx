import React, { useState, useRef, useEffect } from 'react';
import { Button, Input, DatePicker, Space, Divider } from 'antd';
import { ClockCircleOutlined, SearchOutlined, CalendarOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import './styles.css';

const { RangePicker } = DatePicker;

interface TimeRangeOption {
  key: string;
  label: string;
  value: () => [dayjs.Dayjs, dayjs.Dayjs];
}

interface GrafanaTimeRangePickerProps {
  value?: [Date, Date] | null;
  onChange?: (dates: [Date, Date] | null) => void;
  onApply?: (dates: [Date, Date] | null) => void;
  defaultValue?: string;
  style?: React.CSSProperties;
  className?: string;
}

// Time range options similar to <PERSON>ana (Vietnamese)
const timeRangeOptions: TimeRangeOption[] = [
  { key: 'now-15m', label: '15 phút qua', value: () => [dayjs().subtract(15, 'minute'), dayjs()] },
  { key: 'now-30m', label: '30 phút qua', value: () => [dayjs().subtract(30, 'minute'), dayjs()] },
  { key: 'now-1h', label: '1 giờ qua', value: () => [dayjs().subtract(1, 'hour'), dayjs()] },
  { key: 'now-3h', label: '3 giờ qua', value: () => [dayjs().subtract(3, 'hour'), dayjs()] },
  { key: 'now-6h', label: '6 giờ qua', value: () => [dayjs().subtract(6, 'hour'), dayjs()] },
  { key: 'now-12h', label: '12 giờ qua', value: () => [dayjs().subtract(12, 'hour'), dayjs()] },
  { key: 'now-24h', label: '24 giờ qua', value: () => [dayjs().subtract(24, 'hour'), dayjs()] },
  { key: 'now-7d', label: '7 ngày qua', value: () => [dayjs().subtract(7, 'day'), dayjs()] },
  { key: 'now-30d', label: '30 ngày qua', value: () => [dayjs().subtract(30, 'day'), dayjs()] },
  { key: 'today', label: 'Hôm nay', value: () => [dayjs().startOf('day'), dayjs().endOf('day')] },
  { key: 'yesterday', label: 'Hôm qua', value: () => [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { key: 'this-week', label: 'Tuần này', value: () => [dayjs().startOf('week'), dayjs().endOf('week')] },
  { key: 'last-week', label: 'Tuần trước', value: () => [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')] },
  { key: 'this-month', label: 'Tháng này', value: () => [dayjs().startOf('month'), dayjs().endOf('month')] },
  { key: 'last-month', label: 'Tháng trước', value: () => [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] },
];

export const GrafanaTimeRangePicker: React.FC<GrafanaTimeRangePickerProps> = ({
  value,
  onChange,
  onApply,
  defaultValue = 'now-7d',
  style,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedKey, setSelectedKey] = useState(defaultValue);
  const [customRange, setCustomRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [isCustomMode, setIsCustomMode] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Get current display text
  const getCurrentDisplayText = () => {
    if (isCustomMode && customRange) {
      const fromDate = customRange[0].format('DD/MM/YYYY HH:mm');
      const toDate = customRange[1].format('DD/MM/YYYY HH:mm');
      return `${fromDate} ~ ${toDate}`;
    }

    const option = timeRangeOptions.find(opt => opt.key === selectedKey);
    return option ? option.label : '7 ngày qua';
  };

  // Filter options based on search term
  const filteredOptions = timeRangeOptions.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle option selection
  const handleOptionSelect = (option: TimeRangeOption) => {
    setSelectedKey(option.key);
    setIsCustomMode(false);
    const [fromDate, toDate] = option.value();
    const dateRange: [Date, Date] = [fromDate.toDate(), toDate.toDate()];
    
    if (onChange) {
      onChange(dateRange);
    }
    
    setIsOpen(false);
  };

  // Handle custom range apply
  const handleCustomApply = () => {
    if (customRange) {
      setIsCustomMode(true);
      const dateRange: [Date, Date] = [customRange[0].toDate(), customRange[1].toDate()];

      if (onChange) {
        onChange(dateRange);
      }

      if (onApply) {
        onApply(dateRange);
      }
    }
    setIsOpen(false);
  };

  // Handle date picker change with default times
  const handleDatePickerChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    if (dates && dates.length === 2) {
      // Set default times: from date at 00:00:00, to date at 23:59:59
      const fromDate = dates[0].startOf('day'); // 00:00:00
      const toDate = dates[1].endOf('day');     // 23:59:59

      setCustomRange([fromDate, toDate]);
      setIsCustomMode(true);

      const dateRange: [Date, Date] = [fromDate.toDate(), toDate.toDate()];

      if (onChange) {
        onChange(dateRange);
      }

      if (onApply) {
        onApply(dateRange);
      }

      setIsOpen(false);
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Initialize custom range from value
  useEffect(() => {
    if (value && value.length === 2) {
      setCustomRange([dayjs(value[0]), dayjs(value[1])]);
      setIsCustomMode(true);
    }
  }, [value]);

  return (
    <div className={`grafana-time-picker ${className || ''}`} style={style}>
      <Button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="grafana-time-picker-button"
        style={{
          backgroundColor: '#ffffff',
          borderColor: '#d9d9d9',
          color: '#000000',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-start',
          width: '300px',
          height: '32px',
          ...style
        }}
      >
        <Space>
          <ClockCircleOutlined />
          <span>{getCurrentDisplayText()}</span>
        </Space>
      </Button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="grafana-time-picker-dropdown"
          style={{
            position: 'absolute',
            top: '36px',
            left: '0',
            zIndex: 1050,
            backgroundColor: '#ffffff',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
            width: '600px',
            maxHeight: '500px',
            overflow: 'hidden',
            display: 'flex'
          }}
        >
          {/* Left Panel - Quick Ranges */}
          <div style={{
            width: '300px',
            borderRight: '1px solid #d9d9d9',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Search */}
            <div style={{ padding: '12px' }}>
              <Input
                placeholder="Tìm kiếm khoảng thời gian"
                prefix={<SearchOutlined style={{ color: '#8c8c8c' }} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#d9d9d9',
                  color: '#000000'
                }}
                className="grafana-search-input"
              />
            </div>

            {/* Quick Range Options */}
            <div style={{
              flex: 1,
              overflowY: 'auto',
              maxHeight: '400px'
            }}>
              {filteredOptions.map((option) => (
                <div
                  key={option.key}
                  onClick={() => handleOptionSelect(option)}
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    color: selectedKey === option.key && !isCustomMode ? '#1b524f' : '#000000',
                    backgroundColor: selectedKey === option.key && !isCustomMode ? '#e6f7ff' : 'transparent',
                    borderLeft: selectedKey === option.key && !isCustomMode ? '2px solid #1b524f' : '2px solid transparent',
                    fontSize: '13px',
                    transition: 'all 0.2s ease'
                  }}
                  className="grafana-option-item"
                >
                  {option.label}
                </div>
              ))}
            </div>
          </div>

          {/* Right Panel - Absolute Time Range */}
          <div style={{
            width: '300px',
            padding: '12px',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              color: '#000000',
              fontSize: '14px',
              fontWeight: 500,
              marginBottom: '12px'
            }}>
              Khoảng thời gian tuyệt đối
            </div>

            <div style={{ marginBottom: '12px' }}>
              <div style={{
                color: '#8c8c8c',
                fontSize: '12px',
                marginBottom: '4px'
              }}>
                Từ
              </div>
              <Input
                placeholder="now-90d"
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#d9d9d9',
                  color: '#000000',
                  marginBottom: '8px'
                }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <div style={{
                color: '#8c8c8c',
                fontSize: '12px',
                marginBottom: '4px'
              }}>
                Đến
              </div>
              <Input
                placeholder="now"
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#d9d9d9',
                  color: '#000000'
                }}
              />
            </div>

            {/* Date Range Picker Button */}
            <div style={{ marginBottom: '16px' }}>
              <Button
                icon={<CalendarOutlined />}
                onClick={() => setShowDatePicker(!showDatePicker)}
                style={{
                  width: '100%',
                  marginBottom: '8px',
                  backgroundColor: '#ffffff',
                  borderColor: '#d9d9d9',
                  color: '#000000',
                  textAlign: 'left'
                }}
              >
                {customRange && isCustomMode
                  ? `${customRange[0].format('DD/MM/YYYY')} ~ ${customRange[1].format('DD/MM/YYYY')}`
                  : 'Chọn khoảng ngày'
                }
              </Button>

              {showDatePicker && (
                <DatePicker.RangePicker
                  onChange={handleDatePickerChange}
                  placeholder={['Từ ngày', 'Đến ngày']}
                  format="DD/MM/YYYY"
                  style={{ width: '100%' }}
                  allowClear
                  showTime={false}
                  value={customRange}
                />
              )}
            </div>

            <Button
              type="primary"
              onClick={handleCustomApply}
              style={{
                backgroundColor: '#1b524f',
                borderColor: '#1b524f',
                width: '100%'
              }}
            >
              Áp dụng khoảng thời gian
            </Button>

            <Divider style={{ borderColor: '#d9d9d9', margin: '16px 0' }} />

            <div style={{
              color: '#8c8c8c',
              fontSize: '11px',
              lineHeight: '1.4'
            }}>
              Có vẻ như bạn chưa sử dụng bộ chọn thời gian này trước đây. Ngay khi bạn nhập một số khoảng thời gian, các khoảng thời gian đã sử dụng gần đây sẽ xuất hiện ở đây.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GrafanaTimeRangePicker;
