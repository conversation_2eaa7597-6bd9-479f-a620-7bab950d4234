import { useState, useEffect } from 'react';
import { AppManagementApi, AppInfo } from '@/api/app-management';
import { initializeApiToken } from '@/utils/common/storage';

export interface ProducerOption {
  label: string;
  value: string;
}

/**
 * Custom hook for managing producer options
 * Fetches app data from API and formats it for use in select components
 */
export const useProducerOptions = () => {
  const [options, setOptions] = useState<ProducerOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProducers = async () => {
    try {
      setLoading(true);
      setError(null);

      // Initialize token before making API call
      initializeApiToken();

      const response = await AppManagementApi.getAllApps();
      
      if (response.success && response.data) {
        const formattedOptions: ProducerOption[] = response.data.map((app: AppInfo) => ({
          label: app.name,
          value: app.code,
        }));
        
        setOptions(formattedOptions);
      } else {
        setError(response.message || 'Không thể tải danh sách ứng dụng');
      }
    } catch (error) {
      console.error('Error fetching apps:', error);
      setError('Có lỗi xảy ra khi tải danh sách ứng dụng');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducers();
  }, []);

  return {
    options,
    loading,
    error,
    refetch: fetchProducers,
  };
};
