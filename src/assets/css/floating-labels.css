/* Clean Floating Label Styles - Ant Design Compatible */
.floating-label-container {
  position: relative;
  margin-bottom: 12px;
}

.floating-label-container .ant-form-item-label {
  display: none; /* Hide the original label since we're using floating */
}

.floating-input {
  position: relative;
}

.floating-label {
  position: absolute;
  left: 11px;
  top: 50%;
  transform: translateY(-50%);
  background: #fff;
  padding: 0 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  line-height: 1.5715;
  pointer-events: none;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  z-index: 1;
  white-space: nowrap;
  font-family: inherit;
}

.floating-label.focused,
.floating-label.has-value {
  top: -6px;
  transform: translateY(-50%);
  font-size: 12px;
  color: #1b524f;
  background: #fff;
  padding: 0 4px;
}

.floating-label.error {
  color: #ff4d4f;
}

.floating-label.focused.error,
.floating-label.has-value.error {
  color: #ff4d4f;
}

/* Clean Input Field Styling */
.floating-input .ant-input,
.floating-input .ant-select-selector,
.floating-input .ant-picker {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

/* Select field adjustments */
.floating-input .ant-select-selector {
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  position: relative !important;
}

.floating-input .ant-select-selection-placeholder {
  display: none !important;
}

.floating-input .ant-select-selection-item {
  line-height: 1.5715 !important;
  padding-top: 0 !important;
  padding-right: 24px !important;
  display: flex !important;
  align-items: center !important;
}

/* Ensure select wrapper has proper positioning */
.floating-input .ant-select {
  position: relative !important;
}

/* DatePicker adjustments */
.floating-input .ant-picker {
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

.floating-input .ant-picker-input {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

.floating-input .ant-picker-input > input {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  line-height: 1.5715 !important;
}

/* Fix calendar icon alignment */
.floating-input .ant-picker-suffix {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

/* Hide all placeholders - consolidated */
.floating-input .ant-input::placeholder,
.floating-input .ant-picker-input > input::placeholder,
.floating-input .ant-input::-webkit-input-placeholder,
.floating-input .ant-picker-input > input::-webkit-input-placeholder,
.floating-input .ant-input::-moz-placeholder,
.floating-input .ant-picker-input > input::-moz-placeholder,
.floating-input .ant-input:-ms-input-placeholder,
.floating-input .ant-picker-input > input:-ms-input-placeholder {
  display: none !important;
}

/* Focus, hover and interaction states */
.floating-input .ant-input:focus,
.floating-input .ant-select-focused .ant-select-selector,
.floating-input .ant-picker-focused {
  border-color: #1b524f;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2);
  outline: 0;
}

.floating-input .ant-input:hover,
.floating-input .ant-select:hover .ant-select-selector,
.floating-input .ant-picker:hover {
  border-color: #1b524f;
}

.floating-input:hover .floating-label.focused,
.floating-input:hover .floating-label.has-value {
  color: #1b524f;
}



/* Error states */
.floating-input.error .ant-input,
.floating-input.error .ant-select-selector,
.floating-input.error .ant-picker {
  border-color: #ff4d4f;
}

.floating-input.error .ant-input:focus,
.floating-input.error .ant-select-focused .ant-select-selector,
.floating-input.error .ant-picker-focused {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* Disabled states */
.floating-input .ant-input:disabled,
.floating-input .ant-select-disabled .ant-select-selector,
.floating-input .ant-picker-disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.floating-input .ant-input:disabled + .floating-label,
.floating-input .ant-select-disabled + .floating-label,
.floating-input .ant-picker-disabled + .floating-label {
  color: rgba(0, 0, 0, 0.25);
}

/* Additional refinements */
.floating-input .ant-select-selection-search {
  padding-top: 0 !important;
  display: flex !important;
  align-items: center !important;
}

.floating-input .ant-select-selection-search-input {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  line-height: 1.5715 !important;
}

/* Multi-select adjustments */
.floating-input .ant-select-multiple .ant-select-selection-item {
  margin-top: 1px !important;
  margin-bottom: 1px !important;
  display: flex !important;
  align-items: center !important;
}

/* Input text alignment */
.floating-input .ant-input {
  line-height: 1.5715 !important;
}

/* Select arrow icon alignment - direct approach */
.floating-input .ant-select-arrow {
  line-height: 30px !important;
}

.floating-input .ant-select-arrow .anticon {
  vertical-align: middle !important;
  line-height: 1 !important;
}

/* Ensure dropdowns appear above other elements */
.ant-select-dropdown,
.ant-picker-dropdown {
  z-index: 1050;
}

/* Form item spacing */
.floating-label-container .ant-form-item-control {
  margin-bottom: 0;
}

.floating-label-container .ant-form-item-explain,
.floating-label-container .ant-form-item-extra {
  margin-top: 4px;
}


