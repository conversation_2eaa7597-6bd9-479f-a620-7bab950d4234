interface Window {
  // Add custom window properties for environment variables
  __ENV?: {
    REACT_APP_API_URL?: string;
    REACT_APP_BACKEND_URL?: string;
    REACT_APP_BACKEND_URL_SSO?: string;
    [key: string]: string | undefined;
  };
}

// Declare process global to avoid TypeScript errors
declare var process: {
  env: {
    [key: string]: string | undefined;
    NODE_ENV?: string;
    REACT_APP_API_URL?: string;
    REACT_APP_BACKEND_URL_EXTERNAL_SERVICE?: string;
    BACKEND_URL_SSO?: string;
    BACKEND_URL_APP?: string;
    REACT_APP_LOGOUT_REDIRECT_URL?: string;
    ENV?: string;
    PUBLIC_URL?: string;
    API_URL?: string;
  };
};

