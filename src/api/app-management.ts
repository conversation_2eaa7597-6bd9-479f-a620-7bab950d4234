import axios from 'axios';
import { getApiBaseUrl, getApiHeaders } from '@/utils/api';
import { ApiResponse } from './message-monitor';

// App management types
export interface AppInfo {
  name: string;
  code: string;
  description?: string;
}

export interface AppStatus {
  appCode: string;
  lastSentTime?: string;
  lastSyncTime?: string;
  lastTrackingSyncTime?: string;
  lastMessageTime?: string;
  lastTrackingTime?: string;
}

export type JobType = 'SENT' | 'SYNC_MESSAGE' | 'SYNC_TRACKING';

// API Class for App Management
export class AppManagementApi {
  // Get all apps
  static async getAllApps(): Promise<ApiResponse<AppInfo[]>> {
    try {
      const baseUrl = getApiBaseUrl();
      const headers = getApiHeaders();
      
      const response = await axios.get(`${baseUrl}/app`, {
        headers,
      });

      return {
        success: response.data.success,
        message: response.data.message,
        code: response.data.code,
        data: response.data.data
      };
    } catch (error) {
      console.error('Error fetching apps:', error);
      throw error;
    }
  }

  // Get app sync status by app code
  static async getAppSyncStatus(appCode: string): Promise<ApiResponse<AppStatus>> {
    try {
      const baseUrl = getApiBaseUrl();
      const headers = getApiHeaders();
      
      const response = await axios.get(`${baseUrl}/app/${appCode}/status`, {
        headers,
      });

      return {
        success: response.data.success,
        message: response.data.message,
        code: response.data.code,
        data: response.data.data
      };
    } catch (error) {
      console.error('Error fetching app sync status:', error);
      throw error;
    }
  }

  // Get all app sync statuses
  static async getAllAppSyncStatuses(): Promise<ApiResponse<AppStatus[]>> {
    try {
      const baseUrl = getApiBaseUrl();
      const headers = getApiHeaders();
      
      const response = await axios.get(`${baseUrl}/app/status`, {
        headers,
      });

      return {
        success: response.data.success,
        message: response.data.message,
        code: response.data.code,
        data: response.data.data
      };
    } catch (error) {
      console.error('Error fetching all app sync statuses:', error);
      throw error;
    }
  }

  // Reset last runtime for specific app and job type
  static async resetLastRuntime(appCode: string, jobType: JobType): Promise<ApiResponse<void>> {
    try {
      const baseUrl = getApiBaseUrl();
      const headers = getApiHeaders();
      
      const response = await axios.post(`${baseUrl}/app/${appCode}/job/${jobType}/reset`, {}, {
        headers,
      });

      return {
        success: response.data.success,
        message: response.data.message,
        code: response.data.code,
        data: response.data.data
      };
    } catch (error) {
      console.error('Error resetting last runtime:', error);
      throw error;
    }
  }

  // Reset all last runtimes for an app
  static async resetAllLastRuntimeForApp(appCode: string): Promise<ApiResponse<void>> {
    try {
      const baseUrl = getApiBaseUrl();
      const headers = getApiHeaders();
      
      const response = await axios.post(`${baseUrl}/app/${appCode}/job/reset`, {}, {
        headers,
      });

      return {
        success: response.data.success,
        message: response.data.message,
        code: response.data.code,
        data: response.data.data
      };
    } catch (error) {
      console.error('Error resetting all last runtimes for app:', error);
      throw error;
    }
  }
}