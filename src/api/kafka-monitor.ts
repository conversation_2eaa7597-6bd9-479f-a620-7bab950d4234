import api from '../utils/api/baseApi';
import { ApiResponse, BACKEND_URL_KAFKA_MONITOR } from '@/utils';

// Define types for Kafka Monitor API
export interface KafkaMonitorItem {
  id: string;
  topic: string;
  loaiYeuCau: string | null;
  ngayGui: string | null;
  ngayNhanPhanHoi: string | null;
  messageHeaders: string | null;
  messageKey: string | null;
  noiDung: string | null;
  thongTinLoi: string | null;
  guiNhan: number;       // 1 for 'Gửi', 2 for 'Nhận'
  ghiChu: string | null;
  receiveCode: string | null;
  sendCode: string | null;
  maUngDung: string;     // Value from danhsachPhanMem
  thongTinYeuCau: string | null;
  idIoRequest: string | null;
  lanGuiLai: number | null;
  ngayTao: string;
  ngaySua: string | null;
  trangThai: number | null;
  lanKhoiPhuc: number | null;
  maNghiepVu: string | null;
}

// Define search params for Kafka Monitor API
export interface KafkaMonitorSearchParams {
  // Search criteria
  searchText?: string | null;
  maUngDung?: string | null;    // Application code (previously phanMem)
  topic?: string | null;        // Topic name
  trangThai?: number | string | null;
  guiNhan?: number | string | null;  // 1 for 'Gửi', 2 for 'Nhận' (previously loai)
  loaiYeuCau?: string | null;

  // Date filters
  ngayTao?: string;      // Date range for ngayTao in format "dd/MM/yyyy HH:mm:ss,dd/MM/yyyy HH:mm:ss"
  ngayNhanPhanHoi?: string; // Date range for ngayNhanPhanHoi in format "dd/MM/yyyy HH:mm:ss,dd/MM/yyyy HH:mm:ss"

  // Pagination and sorting
  page?: number;
  size?: number;
  sort?: string; // Using a single sort parameter in the format "property,direction"
}

export interface KafkaMonitorSearchResponse {
  content: KafkaMonitorItem[];
  first: boolean;
  last: boolean;
  totalPages: number;
  totalElements: number;
  numberOfElements: number;
}

// Define types for Count Group API
export interface StatusCountItem {
  count: number;
  trangThai: number;
}

export interface StatusCountResponse {
  success: boolean;
  message: string;
  code: string;
  data: StatusCountItem[];
}

/**
 * Kafka Monitor API class for CRUD operations
 */
export class KafkaMonitorApi {
  /**
   * Get all monitoring data with pagination and filtering
   * @param maUngDung - Application ID (required)
   * @param params - Search parameters (optional)
   * @returns Promise with paginated data
   */
  static async getAll(maUngDung: string, params?: Omit<KafkaMonitorSearchParams, 'maUngDung'>): Promise<ApiResponse<KafkaMonitorSearchResponse>> {
    try {
      const url = `${BACKEND_URL_KAFKA_MONITOR}/list/${maUngDung}`;

      // Build URL with query params for logging (filter out undefined values)
      let queryString = '';
      if (params) {
        const filteredParams = Object.entries(params)
          .filter(([_, value]) => value !== undefined && value !== null && value !== '')
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

        if (Object.keys(filteredParams).length > 0) {
          queryString = '?' + new URLSearchParams(filteredParams as any).toString();
        }
      }
      console.log('KafkaMonitor getAll Request:', url + queryString);

      const response = await api.get<ApiResponse<KafkaMonitorSearchResponse>>(url, { params });
      console.log('KafkaMonitor getAll Response:', { url: url + queryString, data: response.data });
      return response.data;
    } catch (error) {
      console.error('KafkaMonitor getAll Error:', { maUngDung, params, error });
      throw error;
    }
  }

  /**
   * Get a single monitoring entry by ID
   * @param maUngDung - Application ID
   * @param id - Entry ID
   * @returns Promise with data details
   */
  static async getById(maUngDung: string, id: string): Promise<ApiResponse<KafkaMonitorItem>> {
    try {
      const url = `${BACKEND_URL_KAFKA_MONITOR}/${maUngDung}/${id}`;
      console.log('KafkaMonitor getById Request:', url);

      const response = await api.get<ApiResponse<KafkaMonitorItem>>(url);
      console.log('KafkaMonitor getById Response:', { url, data: response.data });
      return response.data;
    } catch (error) {
      console.error('KafkaMonitor getById Error:', { maUngDung, id, error });
      throw error;
    }
  }

  /**
   * Resend messages
   * @param maUngDung - Application ID
   * @param ids - Array of message IDs to resend
   * @returns Promise with resend status
   */
  static async resend(maUngDung: string, ids: string[]): Promise<ApiResponse<boolean>> {
    try {
      const url = `${BACKEND_URL_KAFKA_MONITOR}/${maUngDung}`;
      console.log('KafkaMonitor resend Request:', url, 'Body:', { id: ids });

      const response = await api.post<ApiResponse<boolean>>(url, { id: ids });
      console.log('KafkaMonitor resend Response:', { url, data: response.data });
      return response.data;
    } catch (error) {
      console.error('KafkaMonitor resend Error:', { maUngDung, ids, error });
      throw error;
    }
  }

  /**
   * Mark messages as processed
   * @param maUngDung - Application ID
   * @param ids - Array of message IDs to mark as processed
   * @returns Promise with update status
   */
  static async markAsProcessed(maUngDung: string, ids: string[]): Promise<ApiResponse<boolean>> {
    try {
      const url = `${BACKEND_URL_KAFKA_MONITOR}/status/${maUngDung}`;
      console.log('KafkaMonitor markAsProcessed Request:', url, 'Body:', { id: ids });

      const response = await api.post<ApiResponse<boolean>>(url, { id: ids });
      console.log('KafkaMonitor markAsProcessed Response:', { url, data: response.data });
      return response.data;
    } catch (error) {
      console.error('KafkaMonitor markAsProcessed Error:', { maUngDung, ids, error });
      throw error;
    }
  }

  /**
   * Get status count grouped by trangThai for a specific application
   * @param id - Application ID (PhanMemGui)
   * @param params - Additional filter parameters
   * @returns Promise with status count data
   */
  static async getStatusCount(id: string, params?: Partial<KafkaMonitorSearchParams>): Promise<StatusCountResponse> {
    try {
      // Transform URL to countGroup endpoint
      const url = BACKEND_URL_KAFKA_MONITOR.replace('/monitor/kafka', '/monitor/kafka/countGroup') + `/${id}`;

      // Build URL with query params for logging (filter out undefined values)
      let queryString = '';
      if (params) {
        const filteredParams = Object.entries(params)
          .filter(([_, value]) => value !== undefined && value !== null && value !== '')
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

        if (Object.keys(filteredParams).length > 0) {
          queryString = '?' + new URLSearchParams(filteredParams as any).toString();
        }
      }
      console.log('KafkaMonitor getStatusCount Request:', url + queryString);

      const response = await api.get<StatusCountResponse>(url, { params });
      console.log('KafkaMonitor getStatusCount Response:', { url: url + queryString, data: response.data });
      return response.data;
    } catch (error) {
      console.error('KafkaMonitor getStatusCount Error:', { id, params, error });
      throw error;
    }
  }
}

export default KafkaMonitorApi;
