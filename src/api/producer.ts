import api from '../utils/api/baseApi';
import { ApiResponse } from '@/utils';
import { API_BASE } from '@/utils/common/constants';

// Producer interface
export interface Producer {
  code: string;
  name: string;
}

/**
 * Producer API class for managing producer data
 */
export class ProducerApi {
  private static readonly BASE_URL = `${API_BASE}/app`;

  /**
   * Get all producers
   * @returns Promise with list of producers
   */
  static async getAll(): Promise<ApiResponse<Producer[]>> {
    try {
      console.log('Producer getAll Request:', this.BASE_URL);

      // Debug: Check if token is in localStorage
      const token = localStorage.getItem('access_token');
      console.log('Producer API - Token check:', {
        hasToken: !!token,
        tokenLength: token?.length,
        tokenPrefix: token?.substring(0, 20) + '...'
      });

      const response = await api.get<ApiResponse<Producer[]>>(this.BASE_URL);
      console.log('Producer getAll Response:', { url: this.BASE_URL, data: response.data });
      return response.data;
    } catch (error) {
      console.error('Producer getAll Error:', {
        url: this.BASE_URL,
        error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStatus: (error as any)?.response?.status,
        errorData: (error as any)?.response?.data
      });
      throw error;
    }
  }
}
