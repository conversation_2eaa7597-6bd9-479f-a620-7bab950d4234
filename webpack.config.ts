import path from 'path';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import { CleanWebpackPlugin } from 'clean-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import { Configuration, DefinePlugin, IgnorePlugin } from 'webpack';
import 'webpack-dev-server';

import dotenv from 'dotenv';

export default (env: { environment: string; treeshake: string; port: string }): Configuration => {
  const isProduction = env.environment === 'production';
  const enableTreeShaking = env.treeshake === 'yes';

  // Load biến môi trường từ file .env (mặc định sẽ load .env ở root)
  dotenv.config({ path: `.env.${env.environment || 'development'}` });

  let publicPath = env.environment === 'production' ? '' : '/'

  return {
    entry: './src/index.tsx',
    devtool: 'cheap-module-source-map',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: 'bundle.[contenthash].js',
      publicPath: publicPath, // Đường dẫn gốc cho ứng dụng
    },
    resolve: {
      extensions: ['.ts', '.tsx', '.js', '.jsx'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    mode: isProduction ? 'production' : 'development',
    module: {
      rules: [
        // xử lý ts/tsx
        {
          test: /\.(ts|tsx)$/,
          use: 'ts-loader',
          exclude: /node_modules/,
        },
        // xử lý css
        {
          test: /\.css$/,
          use: [
            isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
            'css-loader',
            'postcss-loader', // <-- thêm loader này
          ],
        },
        {
          test: /\.scss$/,
          use: [
            isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
            'css-loader',
            'postcss-loader', // thêm vào để tailwind hoạt động trên scss
            'sass-loader',
          ],
        },
        // xử lý ảnh, font,...
        {
          test: /\.(png|jpe?g|gif|svg|woff2?|eot|ttf|otf)$/,
          type: 'asset/resource',
        },
      ],
    },
    optimization: {
      usedExports: enableTreeShaking, // Kích hoạt Tree Shaking nếu được yêu cầu
    },
    plugins: [
      new CleanWebpackPlugin(),
      new HtmlWebpackPlugin({
        template: 'public/index.html',
        hash: true,
        inject: 'body',
        publicPath: '/',
        favicon: 'public/logo1.png',
      }),
      new MiniCssExtractPlugin({
        filename: '[name].[contenthash].css',
      }),
      new IgnorePlugin({
        resourceRegExp: /^node_modules$|^\.\/git$/,
      }),
      new DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(env.environment),
        'process.env.API_URL': JSON.stringify(process.env.API_URL),
        'process.env.REACT_APP_API_URL': JSON.stringify(process.env.REACT_APP_API_URL),
        'process.env.BACKEND_URL_SSO': JSON.stringify(process.env.BACKEND_URL_SSO),
        'process.env.BACKEND_URL_APP': JSON.stringify(process.env.BACKEND_URL_APP),
        'process.env.REACT_APP_LOGOUT_REDIRECT_URL': JSON.stringify(process.env.REACT_APP_LOGOUT_REDIRECT_URL),
        'process.env.REACT_APP_BACKEND_URL_EXTERNAL_SERVICE': JSON.stringify(process.env.REACT_APP_BACKEND_URL_EXTERNAL_SERVICE),
        'process.env.ENV': JSON.stringify(process.env.ENV),
        'process.env.PUBLIC_URL': JSON.stringify(process.env.PUBLIC_URL || '/monitor'),
      }),
    ],

    // ⚡️ Thêm cấu hình devServer
    devServer: {
      static: {
        directory: path.resolve(__dirname, 'public'), // Thư mục chứa file index.html
      },
      compress: true, // Bật gzip cho file
      port: (env && env.port) || 2222, // Cổng chạy server (thay đổi nếu bạn muốn)
      open: ['/monitor'], // Tự động mở trình duyệt
      hot: true, // HMR - Hot Module Replacement
      historyApiFallback: {
        disableDotRule: true, // <-- thêm dòng này để tránh lỗi URI malformed
        index: '/index.html', // Đúng với basename
      },
    },
  };
};
