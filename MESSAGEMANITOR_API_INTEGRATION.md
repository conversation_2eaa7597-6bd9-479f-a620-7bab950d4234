# Final API Configuration Guide

## Tổng quan

Ứng dụng đã được cấu hình với:
- **API endpoints**: Sử dụng environment variables từ .env files
- **Access token**: Hardcoded trong constants.ts (không từ env)

## Cấu hình hiện tại

### Production/Development Mode (.env.development / .env.production)
- **Base URL**: `http://*************:30999` (từ `REACT_APP_BACKEND_URL_EXTERNAL_SERVICE`)
- **MessageMonitor**: `http://*************:30999/monitor/message`
- **KafkaMonitor**: `http://*************:30999/monitor/kafka`
- **Producer**: `http://*************:30999/app`
- **Auth SSO**: `http://*************:30999/v1/qtud-sso/` (từ `BACKEND_URL_SSO`)
- **Auth APP**: `http://*************:30999/v1/qtud-app/` (từ `BACKEND_URL_APP`)
- **Token**: Hardcoded trong `constants.ts`

### Local Backend Mode (.env.local)
- **Base URL**: `http://localhost:3000`
- **All APIs**: Use local backend endpoints
- **Token**: Dynamic authentication từ hệ thống auth

## Environment Variables

### Các biến môi trường chính

| Variable | Description | Example |
|----------|-------------|---------|
| `REACT_APP_BACKEND_URL_EXTERNAL_SERVICE` | Base URL cho API | `http://*************:30999` |
| `BACKEND_URL_SSO` | SSO API endpoint | `http://*************:30999/v1/qtud-sso/` |
| `BACKEND_URL_APP` | APP API endpoint | `http://*************:30999/v1/qtud-app/` |

### Access Token

Access token được hardcode trong `src/utils/common/constants.ts`:
```typescript
export const API_ACCESS_TOKEN = 'eyJraWQiOiI3NzUxOWM5MC0wYjg4LTQzMzQtOTZjMi1hNzU2MWE1ODUyYWEi...';
```

### Chuyển đổi giữa các môi trường

#### Sử dụng External API (Production/Development)
```bash
# Trong .env.development hoặc .env.production
REACT_APP_BACKEND_URL_EXTERNAL_SERVICE=http://*************:30999
```

#### Sử dụng Local Backend (Local Development)
```bash
# Trong .env.local - set local URLs
REACT_APP_BACKEND_URL_EXTERNAL_SERVICE=http://localhost:3000
```

## Các API đã được migration

### 1. **KafkaMonitor API**
- **External**: `http://*************:30999/monitor/kafka`
- **Local**: `http://localhost:3000/monitor/kafka`

### 2. **MessageMonitor API**
- **External**: `http://*************:30999/monitor/message`
- **Local**: `http://localhost:3000/monitor/message`

### 3. **Producer API**
- **External**: `http://*************:30999/app`
- **Local**: `http://localhost:3000/producer`

### 4. **Auth APIs**
- **SSO External**: `http://*************:30999/v1/qtud-sso/`
- **SSO Local**: Sử dụng BASE_URL + 'v1/qtud-sso/'
- **APP External**: `http://*************:30999/v1/qtud-app/`
- **APP Local**: Sử dụng BASE_URL + 'v1/qtud-app/'

## Cách hoạt động

### Mode với Access Token (Production/Development)
1. Webpack DefinePlugin expose environment variables vào runtime
2. Constants.ts đọc `REACT_APP_ACCESS_TOKEN` từ env
3. Tất cả API endpoints sử dụng URLs từ `REACT_APP_BACKEND_URL_EXTERNAL_SERVICE`
4. `initializeApiToken()` set Bearer token từ `REACT_APP_ACCESS_TOKEN`
5. Tất cả API calls sử dụng configured endpoints với shared Bearer token

### Mode Local Backend (Local Development)
1. Constants.ts không tìm thấy `REACT_APP_ACCESS_TOKEN`
2. API endpoints sử dụng local URLs hoặc fallback URLs
3. `initializeApiToken()` không set token (sử dụng dynamic auth)
4. baseApi.ts sử dụng token từ authentication system
5. Hoạt động như local development ban đầu

## Files đã được cập nhật

- `src/utils/common/constants.ts` - Cấu hình tất cả API endpoints và token
- `src/utils/common/storage.ts` - Logic khởi tạo token cho external API
- `src/containers/AdminPage/RouteApp/index.tsx` - Global token initialization
- `src/api/producer.ts` - Producer API endpoint configuration
- `src/api/kafka-monitor.ts` - KafkaMonitor API (sử dụng BACKEND_URL_KAFKA_MONITOR)
- `src/api/message-monitor.ts` - MessageMonitor API (sử dụng BACKEND_URL_MESSAGE_MONITOR)
- `src/containers/Auth/api.tsx` - Auth APIs endpoint configuration
- `src/hooks/useProducerOptions.ts` - Producer options với token initialization
- `src/pages/MessageMonitor/Dashboard/index.tsx` - MessageMonitor token initialization
- `src/utils/api/baseApi.ts` - Logic authentication tự động

## Lưu ý quan trọng

- **Simplified configuration**: Không còn external_api variables
- **Direct token usage**: Sử dụng trực tiếp access token từ env
- **Environment-driven**: Tất cả cấu hình được quản lý qua .env files
- **Webpack integration**: DefinePlugin expose env vars vào runtime
- **No code changes needed**: Chỉ cần thay đổi .env files
- **Backward compatible**: Local development vẫn hoạt động như cũ

## Environment Files

### .env.development & .env.production
- Sử dụng API `http://*************:30999`
- Access token authentication
- Tất cả APIs point đến configured service

### .env.local (cho local development)
- Sử dụng local backend `http://localhost:3000`
- Dynamic authentication
- Local development workflow

## Webpack Configuration

DefinePlugin trong `webpack.config.ts` expose các env vars:
```javascript
'process.env.REACT_APP_ACCESS_TOKEN': JSON.stringify(process.env.REACT_APP_ACCESS_TOKEN),
'process.env.REACT_APP_BACKEND_URL_EXTERNAL_SERVICE': JSON.stringify(process.env.REACT_APP_BACKEND_URL_EXTERNAL_SERVICE),
'process.env.BACKEND_URL_SSO': JSON.stringify(process.env.BACKEND_URL_SSO),
'process.env.BACKEND_URL_APP': JSON.stringify(process.env.BACKEND_URL_APP),
```
